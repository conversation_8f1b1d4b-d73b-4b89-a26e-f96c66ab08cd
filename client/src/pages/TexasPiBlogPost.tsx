import { useEffect, useState } from "react";
import { useRoute } from "wouter";
import DOMPurify from "dompurify";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Calendar, Clock, ArrowLeft, Share2 } from "lucide-react";

interface Article {
  title: string;
  slug: string;
  summary?: string;
  content: string;
  featured_image_url?: string;
  read_time_minutes?: number;
  published_at?: string;
  blog_categories?: { name: string; slug: string } | null;
}

export default function TexasPiBlogPost() {
  const [, params] = useRoute("/texas-pi/blog/:slug");
  const slug = params?.slug;
  const [article, setArticle] = useState<Article | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    const load = async () => {
      if (!slug) {
        setError("Article not found");
        setIsLoading(false);
        return;
      }
      try {
        const res = await fetch(
          `/api/blog/articles/market/usa/${slug}?language=en`
        );
        if (res.status === 404) {
          setError("Article not found");
          setIsLoading(false);
          return;
        }
        if (!res.ok) throw new Error("Failed to load article");
        const data = await res.json();

        // Enforce PI visibility by category slug
        const piSlug = (
          import.meta.env.VITE_PI_BLOG_CATEGORY_SLUG || "texas-pi"
        ).toLowerCase();
        const catSlug = (data.blog_categories?.slug || "").toLowerCase();
        if (piSlug && catSlug !== piSlug) {
          setError("Article not available in this section");
          setIsLoading(false);
          return;
        }

        setArticle({
          title: data.title,
          slug: data.slug,
          summary: data.summary,
          content: data.content,
          featured_image_url: data.featured_image_url,
          read_time_minutes: data.read_time_minutes,
          published_at: data.published_at,
          blog_categories: data.blog_categories || null,
        });
      } catch {
        setError("Failed to load article");
      } finally {
        setIsLoading(false);
      }
    };
    load();
  }, [slug]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <Navbar />
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading article...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Article Not Found
          </h1>
          <p className="text-gray-600 mb-8">
            The article is not available in this section.
          </p>
          <a href="/texas-pi/blog" className="text-blue-600 underline">
            Back to Texas PI Blog
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar />
      <main className="max-w-4xl mx-auto px-6 py-10">
        <a
          href="/texas-pi/blog"
          className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-1" /> Back to Texas PI Blog
        </a>

        <article className="bg-white rounded-2xl shadow p-6 md:p-10">
          {article.featured_image_url && (
            <div className="mb-6">
              <img
                src={article.featured_image_url}
                alt={article.title}
                className="w-full rounded-xl"
              />
            </div>
          )}
          <div className="flex items-center text-sm text-gray-500 gap-4 mb-3">
            {article.published_at && (
              <span className="inline-flex items-center gap-1">
                <Calendar className="w-4 h-4" />{" "}
                {formatDate(article.published_at)}
              </span>
            )}
            {typeof article.read_time_minutes === "number" && (
              <span className="inline-flex items-center gap-1">
                <Clock className="w-4 h-4" /> {article.read_time_minutes} min
                read
              </span>
            )}
          </div>
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {article.title}
          </h1>
          {article.summary && (
            <p className="text-gray-600 text-lg mb-6">{article.summary}</p>
          )}

          <div
            className="prose prose-blue max-w-none"
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(article.content || ""),
            }}
          />

          <div className="mt-10 flex items-center justify-between">
            <a
              href="/texas-pi/blog"
              className="inline-flex items-center text-blue-600 hover:text-blue-800"
            >
              <ArrowLeft className="w-4 h-4 mr-1" /> Back to Articles
            </a>
            <button
              type="button"
              onClick={() => {
                navigator.share?.({
                  title: article.title,
                  url: window.location.href,
                });
              }}
              className="inline-flex items-center text-gray-600 hover:text-gray-900"
            >
              <Share2 className="w-4 h-4 mr-1" /> Share
            </button>
          </div>
        </article>
      </main>
      <Footer />
    </div>
  );
}
