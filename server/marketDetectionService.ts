import type { Request } from "express";
import type { BlogMarketType, BlogLanguageType } from "../shared/schema.js";

export interface MarketDetectionResult {
  market: BlogMarketType;
  language: BlogLanguageType;
  source: "url" | "geolocation" | "browser" | "default";
  confidence: number; // 0-1 scale
}

export interface GeolocationData {
  country?: string;
  region?: string;
  city?: string;
}

/**
 * Market Detection Service
 * Detects user market and language preferences from URL, geolocation, and browser settings
 */
export class MarketDetectionService {
  // Market-to-country mappings
  private readonly marketCountryMap = {
    usa: ["US", "United States"],
    "be-fr": ["BE", "Belgium"],
    "be-nl": ["BE", "Belgium"],
    global: [], // Global applies to all
  };

  // Language preference mappings for countries
  private readonly countryLanguageMap = {
    US: "en",
    BE: ["fr", "nl"], // Belgium has both French and Dutch
  };

  /**
   * Detect market and language from request
   */
  detectFromRequest(req: Request): MarketDetectionResult {
    // Priority 1: URL path detection (highest confidence)
    const urlDetection = this.detectFromUrl(req.path);
    if (urlDetection) {
      return {
        ...urlDetection,
        source: "url",
        confidence: 0.95,
      };
    }

    // Priority 2: Geolocation detection (medium confidence)
    const geoDetection = this.detectFromGeolocation(req);
    if (geoDetection) {
      return {
        ...geoDetection,
        source: "geolocation",
        confidence: 0.7,
      };
    }

    // Priority 3: Browser language detection (lower confidence)
    const browserDetection = this.detectFromBrowser(req);
    if (browserDetection) {
      return {
        ...browserDetection,
        source: "browser",
        confidence: 0.5,
      };
    }

    // Default fallback
    return {
      market: "global",
      language: "en",
      source: "default",
      confidence: 0.1,
    };
  }

  /**
   * Detect market from URL path
   */
  private detectFromUrl(
    path: string
  ): { market: BlogMarketType; language: BlogLanguageType } | null {
    // Match blog market paths
    const marketPaths = {
      "/blog/usa": {
        market: "usa" as BlogMarketType,
        language: "en" as BlogLanguageType,
      },
      "/blog/belgium-fr": {
        market: "be-fr" as BlogMarketType,
        language: "fr" as BlogLanguageType,
      },
      "/blog/belgium-nl": {
        market: "be-nl" as BlogMarketType,
        language: "nl" as BlogLanguageType,
      },
      "/blog": {
        market: "global" as BlogMarketType,
        language: "en" as BlogLanguageType,
      },
    };

    // Check for exact path matches
    for (const [urlPath, detection] of Object.entries(marketPaths)) {
      if (path.startsWith(urlPath)) {
        return detection;
      }
    }

    return null;
  }

  /**
   * Detect market from geolocation data
   */
  private detectFromGeolocation(
    req: Request
  ): { market: BlogMarketType; language: BlogLanguageType } | null {
    // Extract geolocation from headers (set by CDN/proxy)
    const country =
      this.getHeader(req, "cf-ipcountry") ||
      this.getHeader(req, "x-country-code") ||
      this.getHeader(req, "cloudfront-viewer-country");

    if (!country) return null;

    // Map country to market and language
    switch (country.toUpperCase()) {
      case "US":
        return { market: "usa", language: "en" };

      case "BE": {
        // For Belgium, we need additional logic to determine French vs Dutch
        // Default to French (more common), but could be enhanced with region detection
        const region = this.getHeader(req, "cf-region");
        if (region && this.isDutchRegion(region)) {
          return { market: "be-nl", language: "nl" };
        }
        return { market: "be-fr", language: "fr" };
      }

      default:
        // For other countries, use global market
        return { market: "global", language: "en" };
    }
  }

  /**
   * Detect language from browser Accept-Language header
   */
  private detectFromBrowser(
    req: Request
  ): { market: BlogMarketType; language: BlogLanguageType } | null {
    const acceptLanguage = this.getHeader(req, "accept-language");
    if (!acceptLanguage) return null;

    // Parse Accept-Language header
    const languages = this.parseAcceptLanguage(acceptLanguage);

    for (const lang of languages) {
      switch (lang.toLowerCase()) {
        case "fr":
        case "fr-be":
        case "fr-fr":
          return { market: "global", language: "fr" };

        case "nl":
        case "nl-be":
        case "nl-nl":
          return { market: "global", language: "nl" };

        case "en":
        case "en-us":
        case "en-gb":
        default:
          return { market: "global", language: "en" };
      }
    }

    return null;
  }

  /** Normalize header to a string value */
  private getHeader(req: Request, name: string): string | undefined {
    const value = req.headers[name.toLowerCase() as keyof typeof req.headers];
    if (Array.isArray(value)) return value[0];
    return typeof value === "string" ? value : undefined;
  }

  /**
   * Parse Accept-Language header into ordered language preferences
   */
  private parseAcceptLanguage(acceptLanguage: string): string[] {
    return acceptLanguage
      .split(",")
      .map((lang) => {
        const [language, quality] = lang.trim().split(";q=");
        return {
          language: language.trim(),
          quality: quality ? parseFloat(quality) : 1.0,
        };
      })
      .sort((a, b) => b.quality - a.quality)
      .map((item) => item.language);
  }

  /**
   * Determine if a Belgian region is Dutch-speaking
   */
  private isDutchRegion(region: string): boolean {
    // Belgian regions that are primarily Dutch-speaking
    const dutchRegions = [
      "VLG", // Flanders
      "BRU", // Brussels (bilingual, but we could default to Dutch)
      "ANT", // Antwerp
      "LIM", // Limburg
      "OVL", // East Flanders
      "WVL", // West Flanders
      "VBR", // Flemish Brabant
    ];

    return dutchRegions.includes(region.toUpperCase());
  }

  /**
   * Get market info from market code
   */
  getMarketInfo(market: BlogMarketType): {
    name: string;
    defaultLanguage: BlogLanguageType;
    supportedLanguages: BlogLanguageType[];
    urlPath: string;
  } {
    const marketInfo = {
      global: {
        name: "Global",
        defaultLanguage: "en" as BlogLanguageType,
        supportedLanguages: ["en", "fr", "nl"] as BlogLanguageType[],
        urlPath: "/blog",
      },
      usa: {
        name: "United States",
        defaultLanguage: "en" as BlogLanguageType,
        supportedLanguages: ["en"] as BlogLanguageType[],
        urlPath: "/blog/usa",
      },
      "be-fr": {
        name: "Belgium (French)",
        defaultLanguage: "fr" as BlogLanguageType,
        supportedLanguages: ["fr", "en"] as BlogLanguageType[],
        urlPath: "/blog/belgium-fr",
      },
      "be-nl": {
        name: "Belgium (Dutch)",
        defaultLanguage: "nl" as BlogLanguageType,
        supportedLanguages: ["nl", "en"] as BlogLanguageType[],
        urlPath: "/blog/belgium-nl",
      },
    };

    return marketInfo[market];
  }

  /**
   * Validate if a market-language combination is supported
   */
  isValidMarketLanguage(
    market: BlogMarketType,
    language: BlogLanguageType
  ): boolean {
    const marketInfo = this.getMarketInfo(market);
    return marketInfo.supportedLanguages.includes(language);
  }

  /**
   * Get the best fallback for an invalid market-language combination
   */
  getFallbackMarketLanguage(
    market: BlogMarketType,
    language: BlogLanguageType
  ): {
    market: BlogMarketType;
    language: BlogLanguageType;
  } {
    const marketInfo = this.getMarketInfo(market);

    // If language is supported in this market, keep it
    if (marketInfo.supportedLanguages.includes(language)) {
      return { market, language };
    }

    // Otherwise, use market's default language
    return {
      market,
      language: marketInfo.defaultLanguage,
    };
  }

  /**
   * Generate canonical URL for a market-language combination
   */
  getCanonicalUrl(market: BlogMarketType, slug?: string): string {
    const marketInfo = this.getMarketInfo(market);
    const basePath = marketInfo.urlPath;

    if (slug) {
      return `${basePath}/${slug}`;
    }

    return basePath;
  }
}

// Export singleton instance
export const marketDetectionService = new MarketDetectionService();
