import { useState, useEffect } from "react";
import { useLocation, Link } from "wouter";
import { motion } from "framer-motion";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import ailexIcon from "../assets/ailex-icon.png";

type NavbarProps = {
  currentState?: "TX" | "FL" | "NY";
};

export default function Navbar({ currentState }: NavbarProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [location, setLocation] = useLocation();
  // Check if we're on Texas PI pages
  const isTexasPiPage = location.startsWith("/texas-pi");

  // Get Stripe checkout URL from environment
  const stripeCheckoutUrl = import.meta.env.VITE_STRIPE_CHECKOUT_URL;

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 32);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Function to navigate to homepage or scroll to top
  const handleLogoClick = (e: React.MouseEvent) => {
    e.preventDefault();

    // Small visual feedback animation
    const target = e.currentTarget;
    target.classList.add("scale-105");
    setTimeout(() => {
      target.classList.remove("scale-105");
    }, 200);

    // Check if we're on Texas PI pages
    const isTexasPiPage = location.startsWith("/texas-pi");

    if (isTexasPiPage) {
      if (location === "/texas-pi") {
        // Already on Texas PI homepage, just scroll to top
        window.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      } else {
        // Navigate to Texas PI homepage
        setLocation("/texas-pi");
      }
    } else {
      if (location === "/") {
        // Already on main homepage, just scroll to top
        window.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      } else {
        // Navigate to main homepage
        setLocation("/");
      }
    }
  };

  // Function to navigate to homepage and scroll to section
  const navigateToSection = (sectionId: string) => {
    const isTexasPiPage = location.startsWith("/texas-pi");

    if (isTexasPiPage) {
      // We're on a Texas PI page
      if (location === "/texas-pi") {
        // Already on Texas PI homepage, just scroll to section
        const element = document.getElementById(sectionId);
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      } else {
        // Navigate to Texas PI homepage first, then scroll
        setLocation("/texas-pi");
        setTimeout(() => {
          document.getElementById(sectionId)?.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }, 100);
      }
    } else {
      // We're on main site pages
      if (location === "/") {
        // Already on main homepage, just scroll
        document.getElementById(sectionId)?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      } else {
        // Navigate to main homepage first, then scroll
        setLocation("/");
        setTimeout(() => {
          document.getElementById(sectionId)?.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }, 100);
      }
    }
  };

  return (
    <header
      className={`fixed w-full z-50 top-0 h-16 flex items-center transition-default ${
        isScrolled ? "bg-navy shadow-md" : ""
      }`}
    >
      <div className="container-full w-full flex items-center justify-between">
        <a
          href="#"
          onClick={handleLogoClick}
          className="flex items-center cursor-pointer transition-all duration-200 ease-out"
        >
          <motion.img
            src={ailexIcon}
            alt="AiLex Icon"
            className="h-8 w-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />
          <span
            className={`ml-3 text-lg font-bold tracking-wide ${isScrolled ? "text-white" : "text-navy"}`}
          >
            AiLex
          </span>
          {/* Official Texas State Flag - only show on Texas PI pages */}
          {isTexasPiPage && (
            <div className="ml-3">
              {/* Texas Flag - Official 2:3 Aspect Ratio */}
              <svg
                width="30"
                height="20"
                viewBox="0 0 30 20"
                className="shadow-sm"
              >
                {/* Blue vertical stripe (left 1/3) */}
                <rect x="0" y="0" width="10" height="20" fill="#002868" />

                {/* White horizontal stripe (top right) */}
                <rect x="10" y="0" width="20" height="10" fill="#FFFFFF" />

                {/* Red horizontal stripe (bottom right) */}
                <rect x="10" y="10" width="20" height="10" fill="#BF0A30" />

                {/* White five-pointed star centered in blue section */}
                <g transform="translate(5, 10)">
                  <path
                    d="M0,-4 L1.176,-1.236 L4,-1.236 L1.618,0.472 L2.351,3.236 L0,1.528 L-2.351,3.236 L-1.618,0.472 L-4,-1.236 L-1.176,-1.236 Z"
                    fill="#FFFFFF"
                  />
                </g>
              </svg>
            </div>
          )}
        </a>

        <div className="hidden md:flex items-center space-x-8">
          <button
            onClick={() => navigateToSection("features")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            Features
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
          <button
            onClick={() => navigateToSection("pricing")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            Pricing
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
          <Link
            href={location === "/texas-pi" ? "/texas-pi/blog" : "/blog"}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            Blog
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </Link>
          <button
            onClick={() => navigateToSection("faq")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            FAQ
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
          <button
            onClick={() => navigateToSection("pricing")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            Contact
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
        </div>

        <div className="flex items-center space-x-3">
          <a
            href="/login"
            className="text-sm px-6 py-3 border border-[#1EAEDB] text-[#1EAEDB] rounded-xl hover:bg-[#1EAEDB] hover:text-white transition-all duration-300 ease-out font-medium"
          >
            Login
          </a>
          {stripeCheckoutUrl ? (
            <a href={stripeCheckoutUrl} className="btn-primary text-sm">
              Start free trial
            </a>
          ) : (
            <a href="/login" className="btn-primary text-sm">
              Start free trial
            </a>
          )}

          <Sheet>
            <SheetTrigger asChild>
              <button className="ml-4 md:hidden text-gray-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-6 w-6 ${isScrolled ? "text-white" : ""}`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px]">
              <div className="flex flex-col space-y-4 mt-8">
                <a
                  href="#"
                  onClick={handleLogoClick}
                  className="text-lg font-medium flex items-center transition-all duration-200 ease-out hover:text-primary"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-primary"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Top
                </a>
                <button
                  onClick={() => navigateToSection("features")}
                  className="text-lg font-medium text-left"
                >
                  Features
                </button>
                <button
                  onClick={() => navigateToSection("pricing")}
                  className="text-lg font-medium text-left"
                >
                  Pricing
                </button>
                <button
                  onClick={() => navigateToSection("faq")}
                  className="text-lg font-medium text-left"
                >
                  FAQ
                </button>
                <button
                  onClick={() => navigateToSection("pricing")}
                  className="text-lg font-medium text-left"
                >
                  Contact
                </button>
                <Link
                  href={location === "/texas-pi" ? "/texas-pi/blog" : "/blog"}
                  className="text-lg font-medium"
                >
                  Blog
                </Link>
                <a href="/login" className="text-lg font-medium">
                  Login
                </a>
                {stripeCheckoutUrl ? (
                  <a
                    href={stripeCheckoutUrl}
                    className="btn-primary mt-4 text-center"
                  >
                    Start free trial
                  </a>
                ) : (
                  <a href="/login" className="btn-primary mt-4 text-center">
                    Start free trial
                  </a>
                )}

                {/* State selector in mobile menu */}
                {currentState && (
                  <div className="mt-6">
                    <p className="text-sm font-medium mb-2">Switch State:</p>
                    <div className="flex flex-wrap gap-2">
                      <Link
                        href="/tx"
                        className={`code text-xs px-3 py-1 rounded-full border ${currentState === "TX" ? "bg-primary text-white" : "border-gray-300"}`}
                      >
                        Texas
                      </Link>
                      <Link
                        href="/fl"
                        className={`code text-xs px-3 py-1 rounded-full border ${currentState === "FL" ? "bg-primary text-white" : "border-gray-300"}`}
                      >
                        Florida
                      </Link>
                      <Link
                        href="/ny"
                        className={`code text-xs px-3 py-1 rounded-full border ${currentState === "NY" ? "bg-primary text-white" : "border-gray-300"}`}
                      >
                        New York
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
