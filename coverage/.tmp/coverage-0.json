{"result": [{"scriptId": "1035", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2163, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2163, "count": 1}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1300", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/server/__tests__/ping.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 2814, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 2814, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 666, "endOffset": 1144, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 697, "endOffset": 945, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 975, "endOffset": 1140, "count": 1}, {"startOffset": 1067, "endOffset": 1139, "count": 0}], "isBlockCoverage": true}], "startOffset": 209}, {"scriptId": "1495", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/server/routes.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 115939, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 115939, "count": 1}, {"startOffset": 1772, "endOffset": 1817, "count": 0}, {"startOffset": 1871, "endOffset": 2095, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 311, "endOffset": 358, "count": 1}, {"startOffset": 348, "endOffset": 356, "count": 0}], "isBlockCoverage": true}, {"functionName": "generateSlug", "ranges": [{"startOffset": 2187, "endOffset": 2322, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateReadingTime", "ranges": [{"startOffset": 2323, "endOffset": 2554, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeHtml", "ranges": [{"startOffset": 2555, "endOffset": 3027, "count": 0}], "isBlockCoverage": false}, {"functionName": "registerRoutes", "ranges": [{"startOffset": 3028, "endOffset": 33871, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3202, "endOffset": 3260, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3298, "endOffset": 5135, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5176, "endOffset": 6123, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6160, "endOffset": 6676, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6706, "endOffset": 7418, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7451, "endOffset": 8247, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8345, "endOffset": 9280, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9394, "endOffset": 9956, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10070, "endOffset": 10876, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10984, "endOffset": 13283, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13395, "endOffset": 14136, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14245, "endOffset": 17238, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 17350, "endOffset": 21205, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21320, "endOffset": 21881, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21991, "endOffset": 22543, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22654, "endOffset": 23885, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23999, "endOffset": 25223, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 25340, "endOffset": 26465, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26502, "endOffset": 26811, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26870, "endOffset": 28621, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28689, "endOffset": 29925, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 29968, "endOffset": 30535, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 30639, "endOffset": 31940, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 32051, "endOffset": 32871, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 32991, "endOffset": 33752, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1496", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/server/storage.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5346, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5346, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 307, "endOffset": 350, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 449, "endOffset": 489, "count": 0}], "isBlockCoverage": false}, {"functionName": "MemStorage", "ranges": [{"startOffset": 514, "endOffset": 703, "count": 1}], "isBlockCoverage": true}, {"functionName": "getUser", "ranges": [{"startOffset": 706, "endOffset": 760, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUserByUsername", "ranges": [{"startOffset": 763, "endOffset": 901, "count": 0}], "isBlockCoverage": false}, {"functionName": "createUser", "ranges": [{"startOffset": 904, "endOffset": 1062, "count": 0}], "isBlockCoverage": false}, {"functionName": "createContactSubmission", "ranges": [{"startOffset": 1065, "endOffset": 1477, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1497", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/shared/schema.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27934, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27934, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 302, "endOffset": 340, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 450, "endOffset": 501, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 606, "endOffset": 652, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 758, "endOffset": 805, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 908, "endOffset": 952, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1056, "endOffset": 1101, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1209, "endOffset": 1258, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1379, "endOffset": 1441, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1557, "endOffset": 1614, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1722, "endOffset": 1771, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1887, "endOffset": 1944, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2058, "endOffset": 2113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2228, "endOffset": 2284, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2399, "endOffset": 2455, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2570, "endOffset": 2626, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2734, "endOffset": 2783, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2893, "endOffset": 2944, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3058, "endOffset": 3113, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7013, "endOffset": 7036, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1702", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/server/contentResolutionService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 34581, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 321, "endOffset": 378, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 494, "endOffset": 551, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveArticle", "ranges": [{"startOffset": 1049, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "resolveArticlesList", "ranges": [{"startOffset": 2442, "endOffset": 4167, "count": 0}], "isBlockCoverage": false}, {"functionName": "findArticle", "ranges": [{"startOffset": 4241, "endOffset": 4742, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMarketDefaultLanguage", "ranges": [{"startOffset": 4800, "endOffset": 5094, "count": 0}], "isBlockCoverage": false}, {"functionName": "findTranslatedContent", "ranges": [{"startOffset": 5172, "endOffset": 6451, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAvailableMarkets", "ranges": [{"startOffset": 6507, "endOffset": 6981, "count": 0}], "isBlockCoverage": false}, {"functionName": "getContentFallback<PERSON><PERSON>n", "ranges": [{"startOffset": 7055, "endOffset": 8105, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMarketDefaultLanguageSync", "ranges": [{"startOffset": 8192, "endOffset": 8379, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkContentAvailability", "ranges": [{"startOffset": 8451, "endOffset": 9113, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasTranslationKey", "ranges": [{"startOffset": 9189, "endOffset": 9489, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1703", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/server/marketDetectionService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24275, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24275, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 319, "endOffset": 374, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 488, "endOffset": 543, "count": 0}], "isBlockCoverage": false}, {"functionName": "MarketDetectionService", "ranges": [{"startOffset": 580, "endOffset": 993, "count": 1}], "isBlockCoverage": true}, {"functionName": "detectFromRequest", "ranges": [{"startOffset": 1053, "endOffset": 1767, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFromUrl", "ranges": [{"startOffset": 1815, "endOffset": 2359, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFromGeolocation", "ranges": [{"startOffset": 2415, "endOffset": 3059, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectFromBrowser", "ranges": [{"startOffset": 3131, "endOffset": 3803, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3850, "endOffset": 4029, "count": 0}], "isBlockCoverage": false}, {"functionName": "parseAcceptLanguage", "ranges": [{"startOffset": 4112, "endOffset": 4451, "count": 0}], "isBlockCoverage": false}, {"functionName": "isDutchRegion", "ranges": [{"startOffset": 4519, "endOffset": 4910, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMarketInfo", "ranges": [{"startOffset": 4963, "endOffset": 5691, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidMarketLanguage", "ranges": [{"startOffset": 5766, "endOffset": 5923, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFallbackMarketLanguage", "ranges": [{"startOffset": 6008, "endOffset": 6287, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCanonicalUrl", "ranges": [{"startOffset": 6364, "endOffset": 6571, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1704", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/server/websiteAdminRoutes.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 47692, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 47692, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 320, "endOffset": 376, "count": 15}, {"startOffset": 366, "endOffset": 374, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 494, "endOffset": 553, "count": 1}, {"startOffset": 543, "endOffset": 551, "count": 0}], "isBlockCoverage": true}, {"functionName": "getWebsiteAdminJWTSecret", "ranges": [{"startOffset": 2013, "endOffset": 2210, "count": 0}], "isBlockCoverage": false}, {"functionName": "requireWebsiteAdminAuth", "ranges": [{"startOffset": 2937, "endOffset": 4173, "count": 0}], "isBlockCoverage": false}, {"functionName": "registerWebsiteAdminRoutes", "ranges": [{"startOffset": 4994, "endOffset": 15028, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5208, "endOffset": 7295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7355, "endOffset": 9103, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9204, "endOffset": 11588, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11650, "endOffset": 13659, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13733, "endOffset": 14917, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1796", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/server/emailService.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 17309, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 17309, "count": 1}, {"startOffset": 1231, "endOffset": 1275, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 306, "endOffset": 348, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 525, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 639, "endOffset": 694, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendEmail", "ranges": [{"startOffset": 1324, "endOffset": 1821, "count": 0}], "isBlockCoverage": false}, {"functionName": "generatePasswordResetEmail", "ranges": [{"startOffset": 1823, "endOffset": 6333, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendPasswordResetEmail", "ranges": [{"startOffset": 6335, "endOffset": 6553, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}, {"scriptId": "1798", "url": "file:///Users/<USER>/Documents/Coding/LegalAssistant/server/vite.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11227, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11227, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 300, "endOffset": 336, "count": 2}, {"startOffset": 326, "endOffset": 334, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 437, "endOffset": 479, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 582, "endOffset": 626, "count": 0}], "isBlockCoverage": false}, {"functionName": "log", "ranges": [{"startOffset": 1186, "endOffset": 1462, "count": 2}], "isBlockCoverage": true}, {"functionName": "setupVite", "ranges": [{"startOffset": 1464, "endOffset": 3251, "count": 0}], "isBlockCoverage": false}, {"functionName": "serveStatic", "ranges": [{"startOffset": 3253, "endOffset": 4127, "count": 0}], "isBlockCoverage": false}], "startOffset": 209}]}