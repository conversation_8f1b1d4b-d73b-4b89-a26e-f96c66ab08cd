import { motion } from "framer-motion";
import { Link, useLocation } from "wouter";
import { FaTwitter, FaLinkedinIn } from "react-icons/fa";
import ailexIcon from "../assets/ailex-icon.png";

export default function Footer() {
  const [location, setLocation] = useLocation();

  // Check if we're on Texas PI pages
  const isTexasPiPage = location.startsWith("/texas-pi");

  // Function to navigate to section with context awareness
  const navigateToSection = (sectionId: string) => {
    if (isTexasPiPage) {
      if (location === "/texas-pi") {
        // Already on Texas PI homepage, just scroll to section
        const element = document.getElementById(sectionId);
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      } else {
        // Navigate to Texas PI homepage first, then scroll
        setLocation("/texas-pi");
        setTimeout(() => {
          document.getElementById(sectionId)?.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }, 100);
      }
    } else {
      if (location === "/") {
        // Already on main homepage, just scroll
        document.getElementById(sectionId)?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      } else {
        // Navigate to main homepage first, then scroll
        setLocation("/");
        setTimeout(() => {
          document.getElementById(sectionId)?.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }, 100);
      }
    }
  };
  return (
    <footer className="bg-navy text-white pt-16 pb-8">
      <div className="container-content">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          <div>
            <Link href="/" className="flex items-center mb-4">
              <img src={ailexIcon} alt="AiLex Icon" className="h-8 w-auto" />
              <span className="ml-2 text-lg font-bold">AiLex</span>
            </Link>
            <p className="text-gray-400 text-sm">
              Built for small firms, powered by big tech.
            </p>

            <div className="flex space-x-4 mt-6">
              <a
                href="https://x.com/JustAskAiLex"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primary transition-default"
              >
                <FaTwitter className="h-5 w-5" />
              </a>
              <a
                href="https://www.linkedin.com/company/*********"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-primary transition-default"
              >
                <FaLinkedinIn className="h-5 w-5" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Product</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="#features"
                  onClick={(e) => {
                    e.preventDefault();
                    navigateToSection("features");
                  }}
                  className="text-gray-400 hover:text-white transition-default"
                >
                  Features
                </a>
              </li>
              <li>
                <a
                  href="#pricing"
                  onClick={(e) => {
                    e.preventDefault();
                    navigateToSection("pricing");
                  }}
                  className="text-gray-400 hover:text-white transition-default"
                >
                  Pricing
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Resources</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href={isTexasPiPage ? "/texas-pi/blog" : "/blog"}
                  className="text-gray-400 hover:text-white transition-default"
                >
                  Blog
                </Link>
              </li>
              <li>
                <a
                  href="#faq"
                  onClick={(e) => {
                    e.preventDefault();
                    navigateToSection("faq");
                  }}
                  className="text-gray-400 hover:text-white transition-default"
                >
                  FAQ
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4">Get Weekly AI Tips</h3>
            <p className="text-gray-400 text-sm mb-4">
              Legal tech tips to help your practice grow.
            </p>

            <form className="flex">
              <input
                type="email"
                placeholder="Email address"
                className="px-4 py-2 rounded-l-lg w-full focus:outline-none focus:ring-2 focus:ring-[#1EAEDB] text-gray-900 bg-white"
              />
              <button
                type="button"
                className="bg-[#1EAEDB] hover:bg-[#1EAEDB]/90 text-white px-4 rounded-r-lg transition-colors"
              >
                Subscribe
              </button>
            </form>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-500 text-sm mb-4 md:mb-0 flex flex-wrap items-center gap-4">
            <span>
              © {new Date().getFullYear()} AiLex Law. All rights reserved.
            </span>
            <a href="#" className="hover:text-gray-300 transition-default">
              Privacy Policy
            </a>
            <a href="#" className="hover:text-gray-300 transition-default">
              Terms of Service
            </a>
          </div>

          <div className="flex flex-wrap text-sm text-gray-500">
            <motion.span
              initial={{ opacity: 0.5 }}
              whileHover={{
                opacity: 1,
                color: "#B8FF5C",
                transition: { duration: 0.3 },
              }}
            >
              Made with ❤️ with solo attorneys & small firms in mind
            </motion.span>
          </div>
        </div>
      </div>
    </footer>
  );
}
