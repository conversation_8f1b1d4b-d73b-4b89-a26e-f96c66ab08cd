# Database
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Development
NODE_ENV=development

# Stripe Configuration
VITE_STRIPE_CHECKOUT_URL=

# Preview Protection
VITE_PREVIEW_PASSWORD=

# Texas PI blog configuration
# Slug of the category whose posts appear on the Texas PI pages
VITE_PI_BLOG_CATEGORY_SLUG=texas-pi

# Website Admin JWT secret (required for admin auth)
WEBSITE_ADMIN_JWT_SECRET=
