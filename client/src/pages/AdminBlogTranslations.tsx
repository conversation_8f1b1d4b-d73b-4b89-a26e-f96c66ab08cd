import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import {
  Languages,
  ArrowLeft,
  Edit,
  Check,
  X,
  Globe,
  Flag,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: string;
}

interface BlogArticle {
  id: number;
  title: string;
  slug: string;
  summary: string;
  market: string;
  language: string;
  status: string;
  created_at: string;
}

interface TranslationKey {
  id: string;
  key: string;
  description: string;
  default_content: string;
  show_in_countries: string[];
  content_type: string;
  metadata: { field_type?: string; [key: string]: unknown };
}

interface Translation {
  id: string;
  key_id: string;
  language: string;
  content: string;
  is_auto_translated: boolean;
  is_approved: boolean;
  confidence: number;
  created_at: string;
  updated_at: string;
}

interface TranslationWithKey extends Translation {
  translation_key: TranslationKey;
}

export default function AdminBlogTranslations() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [articles, setArticles] = useState<BlogArticle[]>([]);
  const [translations, setTranslations] = useState<TranslationWithKey[]>([]);
  const [selectedArticle, setSelectedArticle] = useState<BlogArticle | null>(
    null
  );
  const [selectedLanguage, setSelectedLanguage] = useState<string>("fr");
  const [editingTranslation, setEditingTranslation] = useState<string | null>(
    null
  );
  const [editContent, setEditContent] = useState<string>("");

  const verifyAdminToken = async (token: string): Promise<AdminUser | null> => {
    try {
      const response = await fetch("/api/admin/verify", {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (response.ok) {
        const data = await response.json();
        return data.user;
      }
    } catch (error) {
      console.error("Token verification failed:", error);
    }
    return null;
  };

  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem("adminToken");
      if (!token) {
        setLocation("/admin-secret-portal");
        return;
      }

      try {
        const user = await verifyAdminToken(token);
        if (user) {
          setAdminUser(user);
          setIsAuthenticated(true);
          await fetchArticles();
        } else {
          localStorage.removeItem("adminToken");
          localStorage.removeItem("adminUser");
          setLocation("/admin-secret-portal");
        }
      } catch (error) {
        console.error("Auth verification failed:", error);
        setLocation("/admin-secret-portal");
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [setLocation]);

  const fetchArticles = async () => {
    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch("/api/admin/blog/articles?limit=100", {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        // Filter for global English articles that can be translated
        const translatableArticles = data.articles.filter(
          (article: BlogArticle) =>
            article.market === "global" &&
            article.language === "en" &&
            article.status === "published"
        );
        setArticles(translatableArticles);
      }
    } catch (error) {
      console.error("Error fetching articles:", error);
    }
  };

  const fetchTranslations = async (articleSlug: string) => {
    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch(
        `/api/admin/translations?filter=blog.article.%.${articleSlug}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setTranslations(data.translations || []);
      }
    } catch (error) {
      console.error("Error fetching translations:", error);
    }
  };

  const handleArticleSelect = async (article: BlogArticle) => {
    setSelectedArticle(article);
    await fetchTranslations(article.slug);
  };

  const handleEditTranslation = (translation: TranslationWithKey) => {
    setEditingTranslation(translation.id);
    setEditContent(translation.content);
  };

  const handleSaveTranslation = async (translationId: string) => {
    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch(`/api/admin/translations/${translationId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          content: editContent,
          is_approved: true,
        }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Translation updated successfully",
        });
        setEditingTranslation(null);
        if (selectedArticle) {
          await fetchTranslations(selectedArticle.slug);
        }
      } else {
        throw new Error("Failed to update translation");
      }
    } catch (error) {
      console.error("Error saving translation:", error);
      toast({
        title: "Error",
        description: "Failed to update translation",
        variant: "destructive",
      });
    }
  };

  const handleApproveTranslation = async (translationId: string) => {
    try {
      const token = localStorage.getItem("adminToken");
      const response = await fetch(
        `/api/admin/translations/${translationId}/approve`,
        {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (response.ok) {
        toast({
          title: "Success",
          description: "Translation approved",
        });
        if (selectedArticle) {
          await fetchTranslations(selectedArticle.slug);
        }
      }
    } catch (error) {
      console.error("Error approving translation:", error);
      toast({
        title: "Error",
        description: "Failed to approve translation",
        variant: "destructive",
      });
    }
  };

  const getLanguageFlag = (language: string) => {
    const flags = {
      en: "🇺🇸",
      fr: "🇫🇷",
      nl: "🇳🇱",
    };
    return flags[language as keyof typeof flags] || "🌍";
  };

  const getLanguageName = (language: string) => {
    const names = {
      en: "English",
      fr: "Français",
      nl: "Nederlands",
    };
    return names[language as keyof typeof names] || language;
  };

  const getFieldTypeName = (fieldType?: string) => {
    const names = {
      title: "Title",
      summary: "Summary",
      content: "Content",
    };
    return names[(fieldType || "") as keyof typeof names] || fieldType || "";
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLocation("/admin-secret-portal/dashboard")}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Dashboard</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
                <Languages className="w-6 h-6" />
                <span>Blog Translations</span>
              </h1>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="flex items-center space-x-1">
                <Globe className="w-3 h-3" />
                <span>{adminUser?.name}</span>
              </Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Articles List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Flag className="w-5 h-5" />
                  <span>Articles to Translate</span>
                </CardTitle>
                <CardDescription>
                  Select an article to manage its translations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {articles.map((article) => (
                    <div
                      key={article.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedArticle?.id === article.id
                          ? "bg-blue-50 border-blue-200"
                          : "hover:bg-gray-50"
                      }`}
                      onClick={() => handleArticleSelect(article)}
                    >
                      <h3 className="font-medium text-sm">{article.title}</h3>
                      <p className="text-xs text-gray-500 mt-1">
                        {getLanguageFlag("en")} English • Global
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Translation Management */}
          <div className="lg:col-span-2">
            {selectedArticle ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Edit className="w-5 h-5" />
                    <span>Translations for "{selectedArticle.title}"</span>
                  </CardTitle>
                  <CardDescription>
                    Manage translations and approve content for different
                    languages
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Language Filter */}
                    <div className="flex items-center space-x-4">
                      <Label>Filter by Language:</Label>
                      <Select
                        value={selectedLanguage}
                        onValueChange={setSelectedLanguage}
                      >
                        <SelectTrigger className="w-48">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fr">🇫🇷 Français</SelectItem>
                          <SelectItem value="nl">🇳🇱 Nederlands</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Translations Table */}
                    <div className="border rounded-lg">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Field</TableHead>
                            <TableHead>Language</TableHead>
                            <TableHead>Content</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {translations
                            .filter((t) => t.language === selectedLanguage)
                            .map((translation) => (
                              <TableRow key={translation.id}>
                                <TableCell>
                                  <Badge variant="outline">
                                    {getFieldTypeName(
                                      translation.translation_key.metadata
                                        ?.field_type
                                    )}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center space-x-2">
                                    <span>
                                      {getLanguageFlag(translation.language)}
                                    </span>
                                    <span>
                                      {getLanguageName(translation.language)}
                                    </span>
                                  </div>
                                </TableCell>
                                <TableCell className="max-w-md">
                                  {editingTranslation === translation.id ? (
                                    <Textarea
                                      value={editContent}
                                      onChange={(e) =>
                                        setEditContent(e.target.value)
                                      }
                                      rows={3}
                                      className="w-full"
                                    />
                                  ) : (
                                    <div className="truncate">
                                      {translation.content.substring(0, 100)}
                                      {translation.content.length > 100 &&
                                        "..."}
                                    </div>
                                  )}
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center space-x-2">
                                    <Badge
                                      variant={
                                        translation.is_approved
                                          ? "default"
                                          : "secondary"
                                      }
                                    >
                                      {translation.is_approved
                                        ? "Approved"
                                        : "Pending"}
                                    </Badge>
                                    {translation.is_auto_translated && (
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        AI
                                      </Badge>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center space-x-2">
                                    {editingTranslation === translation.id ? (
                                      <>
                                        <Button
                                          size="sm"
                                          onClick={() =>
                                            handleSaveTranslation(
                                              translation.id
                                            )
                                          }
                                        >
                                          <Check className="w-4 h-4" />
                                        </Button>
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={() =>
                                            setEditingTranslation(null)
                                          }
                                        >
                                          <X className="w-4 h-4" />
                                        </Button>
                                      </>
                                    ) : (
                                      <>
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={() =>
                                            handleEditTranslation(translation)
                                          }
                                        >
                                          <Edit className="w-4 h-4" />
                                        </Button>
                                        {!translation.is_approved && (
                                          <Button
                                            size="sm"
                                            onClick={() =>
                                              handleApproveTranslation(
                                                translation.id
                                              )
                                            }
                                          >
                                            <Check className="w-4 h-4" />
                                          </Button>
                                        )}
                                      </>
                                    )}
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center text-gray-500">
                    <Languages className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Select an article to manage its translations</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
