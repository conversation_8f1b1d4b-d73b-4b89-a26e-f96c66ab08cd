@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;700&family=IBM+Plex+Mono:wght@500&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Marquee animation for the state flags banner */
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 35s linear infinite;
  min-width: 100%;
  will-change: transform;
}

/* Custom card rotation classes */
.card-1 {
  transform: rotate(2deg);
  transition: transform 0.3s ease;
}

.card-2 {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.card-3 {
  transform: rotate(-2deg);
  transition: transform 0.3s ease;
}

/* Clear rotation on hover */
.card-1:hover,
.card-2:hover,
.card-3:hover {
  transform: rotate(0deg) translateY(-4px);
}

/* Blog post prose styling */
.prose {
  color: #374151;
  line-height: 1.75;
}

.prose h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.prose h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.prose h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.prose p {
  margin-bottom: 1.25rem;
  line-height: 1.75;
}

.prose ul, .prose ol {
  margin-bottom: 1.25rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.prose ul li {
  list-style-type: disc;
}

.prose ol li {
  list-style-type: decimal;
}

.prose blockquote {
  border-left: 4px solid #1EAEDB;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  background-color: #f8fafc;
  padding: 1rem;
  border-radius: 0.5rem;
}

.prose blockquote p {
  margin-bottom: 0;
  color: #4b5563;
}

.prose strong {
  font-weight: 600;
  color: #111827;
}

.prose em {
  font-style: italic;
  color: #4b5563;
}

:root {
  /* Custom AiLex brand colors */
  --primary: 195 90% 52%; /* #1EAEDB */
  --primary-foreground: 0 0% 100%; /* White text on primary */

  --navy: 210 59% 12%; /* #0C1C2D */
  --cool-gray: 220 20% 97%; /* #F5F7FA */
  --lime: 84 100% 68%; /* #B8FF5C */

  /* Base colors */
  --background: 0 0% 100%;
  --foreground: 210 59% 12%;
  --muted: 220 20% 97%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 210 59% 12%;
  --card: 0 0% 100%;
  --card-foreground: 210 59% 12%;
  --border: 220 20% 97%;
  --input: 220 20% 97%;
  --secondary: 220 20% 97%;
  --secondary-foreground: 210 59% 12%;
  --accent: 84 100% 68%;
  --accent-foreground: 210 59% 12%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 210 59% 12%;
  --radius: 0.75rem;
}

.dark {
  --background: 210 59% 12%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 210 59% 12%;
  --popover-foreground: 0 0% 98%;
  --card: 210 59% 12%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 84 100% 68%;
  --accent-foreground: 210 59% 12%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Inter", sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Inter", sans-serif;
    font-weight: 700;
  }

  .code {
    font-family: "IBM Plex Mono", monospace;
    font-weight: 500;
  }

  .container-content {
    @apply max-w-[1040px] mx-auto px-4 md:px-8;
  }

  .container-full {
    @apply max-w-[1280px] mx-auto px-4 md:px-8;
  }
}

@layer utilities {
  .transition-default {
    transition: all 250ms ease-out;
  }

  .grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary hover:bg-[#0C1C2D] hover:border hover:border-white text-white px-6 py-3 rounded-xl font-medium transition-default border border-transparent;
  }

  .btn-secondary {
    @apply border border-primary text-primary hover:bg-primary hover:bg-opacity-10 px-6 py-3 rounded-xl font-medium transition-default;
  }

  .btn-lime {
    @apply bg-[#B8FF5C] hover:bg-opacity-90 text-navy px-6 py-3 rounded-xl font-medium transition-default;
  }

  .card-hover {
    @apply transition-default hover:shadow-md;
  }

  .feature-card {
    @apply bg-navy bg-opacity-50 rounded-xl p-6 border border-gray-700 hover:border-primary transition-default relative overflow-hidden;
  }
}

/* Animation keyframes */
@keyframes pulse {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.7;
  }
  50% {
    transform: translateY(10px);
    opacity: 1;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes scale {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* Animation classes */
.animate-pulse-slow {
  animation: pulse 3s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-scale {
  animation: scale 10s ease-in-out infinite;
}

.animate-blink {
  animation: blink 1s infinite;
}
