import { useState, useEffect } from "react";
import { motion } from "framer-motion";

interface PreviewPasswordGateProps {
  children: React.ReactNode;
}

export default function PreviewPasswordGate({
  children,
}: PreviewPasswordGateProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  const previewPassword = import.meta.env.VITE_PREVIEW_PASSWORD;
  const isPreview =
    import.meta.env.MODE !== "production" ||
    import.meta.env.VITE_VERCEL_ENV === "preview";

  useEffect(() => {
    // Check if we're in preview mode and need password protection
    if (!isPreview || !previewPassword) {
      setIsAuthenticated(true);
      setIsLoading(false);
      return;
    }

    // Check if already authenticated in session storage
    const storedAuth = sessionStorage.getItem("preview-authenticated");
    if (storedAuth === "true") {
      setIsAuthenticated(true);
    }
    setIsLoading(false);
  }, [isPreview, previewPassword]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (password === previewPassword) {
      setIsAuthenticated(true);
      sessionStorage.setItem("preview-authenticated", "true");
    } else {
      setError("Invalid password");
      setPassword("");
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F5F7FA] via-white to-[#E8F4FD]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1EAEDB]"></div>
      </div>
    );
  }

  if (!isAuthenticated && isPreview && previewPassword) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F5F7FA] via-white to-[#E8F4FD] p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/80 backdrop-blur-sm rounded-xl shadow-[0px_10px_30px_rgba(0,0,0,0.04)] p-8 w-full max-w-md"
        >
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-navy mb-2">
              Preview Access
            </h1>
            <p className="text-gray-600">
              This is a preview deployment. Please enter the password to
              continue.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Password
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#1EAEDB] focus:border-transparent outline-none transition-all"
                placeholder="Enter preview password"
                required
              />
            </div>

            {error && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-red-600 text-sm text-center"
              >
                {error}
              </motion.div>
            )}

            <button type="submit" className="w-full btn-primary py-3">
              Access Preview
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              This page is protected to prevent search engine indexing during
              development.
            </p>
          </div>
        </motion.div>
      </div>
    );
  }

  return <>{children}</>;
}
