/*
  Seed Texas PI blog content directly into Supabase tables using service-role key.
  Requirements:
  - SUPABASE_URL and SUPABASE_KEY in environment (service-role key)
  - PI category slug configured via VITE_PI_BLOG_CATEGORY_SLUG (or PI_CATEGORY_SLUG) default 'texas-pi'
*/

import { createClient } from "@supabase/supabase-js";

type Category = { id: number; name: string; slug: string; color?: string };

const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const PI_CATEGORY_SLUG = (
  process.env.PI_CATEGORY_SLUG ||
  process.env.VITE_PI_BLOG_CATEGORY_SLUG ||
  "texas-pi"
).toLowerCase();

if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error("SUPABASE_URL and S<PERSON>ABASE_KEY are required in env");
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function ensureCategory(): Promise<Category> {
  const { data: categories, error } = await supabase
    .from("blog_categories")
    .select("id,name,slug,color");
  if (error) throw error;
  const existing = (categories || []).find(
    (c) => (c as Category).slug?.toLowerCase() === PI_CATEGORY_SLUG
  ) as Category | undefined;
  if (existing) return existing;
  const { data, error: insErr } = await supabase
    .from("blog_categories")
    .insert({
      name: "Texas PI",
      slug: PI_CATEGORY_SLUG,
      description: "Texas Personal Injury Blog",
      color: "#0C1C2D",
    } as unknown as Record<string, unknown>)
    .select("id,name,slug,color")
    .single();
  if (insErr) throw insErr;
  return data as unknown as Category;
}

async function getAnyAuthorId(): Promise<string> {
  const { data, error } = await supabase
    .from("website_admins")
    .select("id")
    .limit(1)
    .single();
  if (error || !data)
    throw new Error("No website_admins found to use as author_id");
  return String((data as { id: string | number }).id);
}

async function articleExists(slug: string): Promise<boolean> {
  const { data, error } = await supabase
    .from("blog_articles")
    .select("id")
    .eq("slug", slug)
    .maybeSingle();
  if (error) return false;
  return Boolean(data);
}

type SeedPost = {
  title: string;
  slug: string;
  summary: string;
  image?: string;
  content: string;
  readTime?: number;
};

const soloMedicalBacklogs: string = `
  <h2>Why Medical Records Are the Bottleneck in Texas PI Cases</h2>
  <p>For most Texas personal injury attorneys — especially solos and small firms — medical records are the single biggest time sink. A typical case can involve thousands of pages of hospital records, billing statements, diagnostic reports, and physician notes. Reviewing all of it thoroughly is critical, but doing it manually can swallow 12+ hours per case.</p>
  <p>The problem isn't just time. It's risk. Missing a critical entry — like a pre-existing condition note buried on page 437 — can undermine damages or even tank a settlement.</p>
  <h3>The Solo Attorney's Dilemma</h3>
  <p>Large PI firms often assign teams to comb through records. Solo attorneys don't have that luxury. Every extra hour spent on records is an hour not spent on client intake, negotiations, or courtroom prep.</p>
  <h3>A Smarter Way: Structured Workflows + AI Assistance</h3>
  <h4>1. Structured Intake First</h4>
  <ul>
    <li>Always request medical records in a structured format when possible (by facility, by date, by type).</li>
    <li>Use a simple checklist or tool to log what's received vs. what's missing.</li>
  </ul>
  <h4>2. Prioritize What Matters</h4>
  <ul>
    <li>Start with ER visits, surgical reports, and physician summaries.</li>
    <li>Move to billing statements to quickly map economic damages.</li>
  </ul>
  <h4>3. AI-Powered Summaries</h4>
  <ul>
    <li>Use AI to scan and organize records in hours, not days.</li>
    <li>Review AI-generated timelines, keyword highlights, and red flags.</li>
  </ul>
  <h4>4. Human + Machine Review</h4>
  <ul>
    <li>Let AI do the heavy lifting (sorting, highlighting, summarizing).</li>
    <li>The attorney's role shifts from reader of every page to reviewer of what matters.</li>
  </ul>
  <h3>Case Example: From 14 Hours to 2.5</h3>
  <p>A solo PI lawyer in San Antonio cut initial review time from 14 hours to 2.5 using structured intake + AI. Result: quicker demand letters, faster case progression, and more client-facing time.</p>
`;

const chapter74Content: string = `
  <h2>Why Chapter 74 Keeps PI Attorneys Up at Night</h2>
  <p>Texas Civil Practice & Remedies Code Chapter 74 requires a detailed expert report within 120 days of filing a medical malpractice claim. Miss the deadline, and your case can be dismissed.</p>
  <p>Common pitfalls include identifying the right expert quickly, ensuring statutory sufficiency, and juggling multiple deadlines with no margin for error.</p>
  <h3>Key Amendments in 2023 You Must Know</h3>
  <ul>
    <li><strong>Expert qualifications tightened</strong>— More emphasis on board certification in relevant specialties.</li>
    <li><strong>Report sufficiency standards clarified</strong>— Vague or generalized reports face dismissal.</li>
    <li><strong>Defense challenges streamlined</strong>— Early objections with shorter response windows.</li>
  </ul>
  <h3>Practical Steps</h3>
  <h4>1. Build a Trusted Expert Network</h4>
  <ul>
    <li>Maintain a shortlist across specialties (ER, orthopedics, neurology).</li>
    <li>Use bar/medical associations and trusted referrals for speed.</li>
  </ul>
  <h4>2. Use AI to Pre-Screen Records</h4>
  <ul>
    <li>Generate summaries and timelines to guide expert review.</li>
  </ul>
  <h4>3. Automate Deadline Tracking</h4>
  <ul>
    <li>Use calendaring tools to track 90/60/30-day reminders.</li>
  </ul>
  <h3>Case Example: From Panic to Precision</h3>
  <p>After adopting structured workflows and automated calendaring, one firm reduced dismissals to zero and consistently met report deadlines with quality expert submissions.</p>
`;

const seedPosts: SeedPost[] = [
  {
    title:
      "The Solo Attorney's Guide to Beating Texas-Sized Medical Record Backlogs",
    slug: "solo-attorney-beating-medical-record-backlogs",
    summary:
      "Texas PI cases often hinge on thousands of pages of hospital and billing records. Learn how solos cut review time from 12+ hours to under 3 without missing critical details.",
    image:
      "https://images.unsplash.com/photo-**********-6726b3ff858f?auto=format&fit=crop&w=2026&q=80",
    content: soloMedicalBacklogs,
    readTime: 8,
  },
  {
    title:
      "Medical Malpractice in Texas: Chapter 74 Compliance Without the Stress",
    slug: "medical-malpractice-chapter-74-compliance",
    summary:
      "Chapter 74 requirements trip up even seasoned lawyers. This guide breaks down updates and shows how Texas solos stay compliant while controlling costs.",
    image:
      "https://images.unsplash.com/photo-1576091160550-2173dba999ef?auto=format&fit=crop&w=2070&q=80",
    content: chapter74Content,
    readTime: 10,
  },
  {
    title:
      "AI-Powered Demand Letters: Giving Texas Solos the Firepower of Big PI Firms",
    slug: "ai-powered-demand-letters-texas-solos",
    summary:
      "Large firms throw teams at demand letters. Here's how AI-driven drafting helps solos boost settlement values with polished presentations at a fraction of the time.",
    image:
      "https://images.unsplash.com/photo-**********-e076c223a692?auto=format&fit=crop&w=2070&q=80",
    content:
      "<p>Demand letters set tone and valuation. With AI-backed timelines and injury summaries, solos can produce compelling drafts faster, focusing on strategy.</p>",
    readTime: 7,
  },
  {
    title: "Key Texas PI Rulings Every Solo Attorney Should Know in 2025",
    slug: "key-texas-pi-rulings-2025",
    summary:
      "From comparative negligence to pain-and-suffering caps, this roundup explains what's changing — and how Texas solos can adapt strategies to keep cases moving.",
    image:
      "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?auto=format&fit=crop&w=2070&q=80",
    content:
      "<p>A concise overview of notable Texas PI decisions, with practical impacts and checklists for motion practice and negotiations.</p>",
    readTime: 9,
  },
  {
    title: "Winning Back Your Week: Time Management for Texas PI Solos",
    slug: "winning-back-your-week-time-management",
    summary:
      "Between client intake, discovery deadlines, and court prep, solos are stretched thin. See how top PI attorneys reclaim 10+ hours/week with better workflows.",
    image:
      "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?auto=format&fit=crop&w=2070&q=80",
    content:
      "<p>Use structured intake, batching, and light AI to cut admin time and focus on case strategy and client advocacy.</p>",
    readTime: 6,
  },
];

async function createArticle(
  authorId: string,
  categoryId: number,
  post: SeedPost
) {
  if (await articleExists(post.slug)) {
    console.log(`Skipping existing article: ${post.slug}`);
    return;
  }
  const now = new Date().toISOString();
  const { error } = await supabase.from("blog_articles").insert({
    title: post.title,
    slug: post.slug,
    summary: post.summary,
    content: post.content,
    category_id: categoryId,
    author_id: authorId,
    status: "published",
    featured_image_url: post.image,
    meta_title: post.title,
    meta_description: post.summary,
    read_time_minutes: post.readTime,
    market: "usa",
    language: "en",
    is_translation: false,
    published_at: now,
    created_at: now,
    updated_at: now,
  } as unknown as Record<string, unknown>);
  if (error) throw error;
  console.log(`Created article: ${post.slug}`);
}

async function main() {
  console.log(
    `Seeding Texas PI posts to Supabase (category: ${PI_CATEGORY_SLUG})`
  );
  const category = await ensureCategory();
  const authorId = await getAnyAuthorId();
  for (const post of seedPosts) {
    await createArticle(authorId, category.id, post);
  }
  console.log("Seeding complete.");
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
