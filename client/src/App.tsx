import { Switch, Route } from "wouter";
import { Suspense, lazy } from "react";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";

const Home = lazy(() => import("@/pages/Home"));
const StatePage = lazy(() => import("@/pages/StatePage"));
const TexasPiPage = lazy(() => import("@/pages/TexasPiPage"));
const TexasPiBlog = lazy(() => import("@/pages/TexasPiBlog"));
const TexasPiBlogPost = lazy(() => import("@/pages/TexasPiBlogPost"));
const MarketBlogRouter = lazy(() => import("@/components/MarketBlogRouter"));

const Login = lazy(() => import("@/pages/Login"));
const SecurityWhitepaper = lazy(() => import("@/pages/SecurityWhitepaper"));
const AdminLogin = lazy(() => import("@/pages/AdminLogin"));
const AdminForgotPassword = lazy(() => import("@/pages/AdminForgotPassword"));
const AdminResetPassword = lazy(() => import("@/pages/AdminResetPassword"));
const AdminDashboard = lazy(() => import("@/pages/AdminDashboard"));
const AdminBlogArticles = lazy(() => import("@/pages/AdminBlogArticles"));
const AdminBlogEditor = lazy(() => import("@/pages/AdminBlogEditor"));
const AdminBlogCategories = lazy(() => import("@/pages/AdminBlogCategories"));
const AdminBlogTranslations = lazy(
  () => import("@/pages/AdminBlogTranslations")
);
const AdminImageGallery = lazy(() => import("@/pages/AdminImageGallery"));
const NotFound = lazy(() => import("@/pages/not-found"));

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/texas-pi" component={TexasPiPage} />
      <Route path="/texas-pi/blog" component={TexasPiBlog} />
      <Route path="/texas-pi/blog/:slug" component={TexasPiBlogPost} />
      <Route path="/tx" component={() => <StatePage state="TX" />} />
      <Route path="/fl" component={() => <StatePage state="FL" />} />
      <Route path="/ny" component={() => <StatePage state="NY" />} />
      {/* Blog routes with market support */}
      <Route path="/blog" component={MarketBlogRouter} />
      <Route path="/blog/:slug" component={MarketBlogRouter} />
      <Route path="/blog/usa" component={MarketBlogRouter} />
      <Route path="/blog/usa/:slug" component={MarketBlogRouter} />
      <Route path="/blog/belgium-fr" component={MarketBlogRouter} />
      <Route path="/blog/belgium-fr/:slug" component={MarketBlogRouter} />
      <Route path="/blog/belgium-nl" component={MarketBlogRouter} />
      <Route path="/blog/belgium-nl/:slug" component={MarketBlogRouter} />
      <Route path="/login" component={Login} />
      <Route path="/security-whitepaper" component={SecurityWhitepaper} />
      <Route path="/admin-secret-portal" component={AdminLogin} />
      <Route
        path="/admin-secret-portal/forgot-password"
        component={AdminForgotPassword}
      />
      <Route
        path="/admin-secret-portal/reset-password/:token"
        component={AdminResetPassword}
      />
      <Route path="/admin-secret-portal/dashboard" component={AdminDashboard} />
      <Route
        path="/admin-secret-portal/dashboard/blog/articles"
        component={AdminBlogArticles}
      />
      <Route
        path="/admin-secret-portal/dashboard/blog/articles/new"
        component={AdminBlogEditor}
      />
      <Route
        path="/admin-secret-portal/dashboard/blog/articles/:id/edit"
        component={AdminBlogEditor}
      />
      <Route
        path="/admin-secret-portal/dashboard/blog/categories"
        component={AdminBlogCategories}
      />
      <Route
        path="/admin-secret-portal/dashboard/blog/translations"
        component={AdminBlogTranslations}
      />
      <Route
        path="/admin-secret-portal/dashboard/images"
        component={AdminImageGallery}
      />
      {/* Fallback to 404 */}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Suspense fallback={<div style={{ padding: 24 }}>Loading…</div>}>
          <Router />
        </Suspense>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
