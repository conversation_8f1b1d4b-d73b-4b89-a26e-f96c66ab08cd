import { motion } from "framer-motion";
import { useEffect, useMemo, useState } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Calendar, Clock, ArrowRight } from "lucide-react";
interface Category {
  id: number;
  name: string;
  slug: string;
  color?: string;
}
interface ArticleListItem {
  id: number;
  title: string;
  slug: string;
  summary?: string;
  featured_image_url?: string;
  read_time_minutes?: number;
  published_at?: string;
  blog_categories?: {
    id: number;
    name: string;
    slug: string;
    color?: string;
  } | null;
}

export default function TexasPiBlog() {
  const [selectedCategory, setSelectedCategory] = useState<string>("All");
  const [_piCategory, setPiCategory] = useState<Category | null>(null);
  const [articles, setArticles] = useState<ArticleListItem[]>([]);
  const [_isLoading, setIsLoading] = useState(true);
  const [_error, setError] = useState("");

  // Resolve PI category and fetch articles from CMS
  useEffect(() => {
    const run = async () => {
      try {
        // Category slug configured via env (default 'texas-pi')
        const piSlug = import.meta.env.VITE_PI_BLOG_CATEGORY_SLUG || "texas-pi";

        // Get categories to find PI category id
        const catRes = await fetch("/api/blog/categories");
        if (!catRes.ok) throw new Error("Failed to load categories");
        const catData: { categories: Category[] } = await catRes.json();
        const found =
          catData.categories.find(
            (c) => c.slug.toLowerCase() === String(piSlug).toLowerCase()
          ) || null;
        setPiCategory(found);

        // Build URL for USA/en market with optional category filter
        const params = new URLSearchParams({
          language: "en",
          page: "1",
          limit: "20",
        });
        if (found) params.set("categoryId", String(found.id));
        const listRes = await fetch(
          `/api/blog/articles/market/usa?${params.toString()}`
        );
        if (!listRes.ok) throw new Error("Failed to load articles");
        const listJson: { articles: ArticleListItem[] } = await listRes.json();
        setArticles(listJson.articles || []);
      } catch {
        setError("Failed to load Texas PI blog");
      } finally {
        setIsLoading(false);
      }
    };
    run();
  }, []);

  const categories = useMemo(() => {
    const set = new Set<string>(["All"]);
    for (const a of articles) {
      if (a.blog_categories?.name) set.add(a.blog_categories.name);
    }
    return Array.from(set);
  }, [articles]);

  const filteredArticles = useMemo(() => {
    if (selectedCategory === "All") return articles;
    return articles.filter((a) => a.blog_categories?.name === selectedCategory);
  }, [articles, selectedCategory]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar currentState="TX" />

      {/* Hero Section */}
      <section className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <a
              href="/texas-pi"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-8"
            >
              <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
              Back to Texas PI
            </a>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-center"
          >
            <div className="mb-6 bg-blue-100 text-blue-700 hover:bg-blue-200 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              Texas PI Legal Insights
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Resources for Texas Personal Injury Attorneys
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
              Practical insights, case law updates, and expert guidance
              specifically for Texas personal injury practitioners.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="pb-8">
        <div className="max-w-6xl mx-auto px-6">
          <div className="flex flex-wrap gap-3 justify-center">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? "bg-blue-600 text-white shadow-lg"
                    : "bg-white text-gray-600 hover:bg-gray-100 border border-gray-200 shadow-sm"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="pb-20">
        <div className="max-w-6xl mx-auto px-6">
          {filteredArticles.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-gray-400 mb-4">
                <svg
                  className="w-16 h-16 mx-auto"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                No articles in this category
              </h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                Try selecting a different category to see more articles.
              </p>
            </div>
          ) : (
            <div className="grid gap-8 md:gap-10">
              {filteredArticles.map((article, index) => (
                <motion.article
                  key={article.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.06)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.12)] transition-all duration-300 p-8 border border-white/20"
                >
                  <div className="flex flex-col lg:flex-row gap-6">
                    {/* Featured Image */}
                    {article.featured_image_url && (
                      <div className="lg:w-80 lg:flex-shrink-0">
                        <div className="aspect-video lg:aspect-square rounded-xl overflow-hidden bg-gray-100">
                          <img
                            src={article.featured_image_url}
                            alt={article.title}
                            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                            onError={(e) => {
                              e.currentTarget.style.display = "none";
                            }}
                          />
                        </div>
                      </div>
                    )}

                    {/* Content */}
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div
                          className="border-blue-200 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border"
                          style={{
                            color: "#3b82f6",
                            borderColor: "#3b82f640",
                          }}
                        >
                          {article.blog_categories?.name || "General"}
                        </div>
                        <div className="flex items-center text-sm text-gray-500 gap-4">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {article.published_at
                              ? formatDate(article.published_at)
                              : ""}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {(article.read_time_minutes || 0) + " min read"}
                          </div>
                        </div>
                      </div>

                      <h2 className="text-2xl font-bold text-gray-900 mb-3 leading-tight">
                        {article.title}
                      </h2>

                      <p className="text-gray-600 mb-6 leading-relaxed text-lg">
                        {article.summary}
                      </p>

                      <div className="flex items-center justify-between">
                        <span />
                        <a
                          href={`/texas-pi/blog/${article.slug}`}
                          className="bg-[#0C1C2D] hover:bg-[#0C1C2D]/90 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center gap-2 group transition-all duration-300"
                        >
                          Read Article
                          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                        </a>
                      </div>
                    </div>
                  </div>
                </motion.article>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="pb-20">
        <div className="max-w-4xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-gradient-to-r from-[#0C1C2D] to-[#1a2b3d] rounded-2xl p-8 md:p-12 text-center text-white"
          >
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Transform Your Texas PI Practice?
            </h3>
            <p className="text-blue-100 mb-8 text-lg max-w-2xl mx-auto">
              Join hundreds of Texas attorneys who are already using AiLex to
              streamline their medical records review, draft compelling demand
              letters, and maximize case values.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="/login"
                className="bg-[#B8FF5C] hover:bg-[#B8FF5C]/90 text-[#0C1C2D] font-semibold px-8 py-3 rounded-lg transition-all duration-300"
              >
                Start Your Free Trial
              </a>
              <a
                href="/texas-pi"
                className="inline-flex items-center text-blue-100 hover:text-white transition-colors"
              >
                <ArrowRight className="w-4 h-4 mr-2 rotate-180" />
                Back to Texas PI
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
