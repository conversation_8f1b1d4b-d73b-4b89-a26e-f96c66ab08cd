import "dotenv/config";
import express, {
  type Request,
  type Response,
  type NextFunction,
} from "express";
import { registerRoutes } from "./routes.js";
import { setupVite, serveStatic, log } from "./vite.js";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Preview protection middleware - add noindex headers for preview deployments
app.use((req, res, next) => {
  const isPreview =
    process.env.NODE_ENV !== "production" ||
    process.env.VERCEL_ENV === "preview";

  if (isPreview) {
    res.setHeader("X-Robots-Tag", "noindex, nofollow, noarchive");
  }

  next();
});

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: unknown | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson: unknown, ..._args: unknown[]) {
    capturedJsonResponse = bodyJson;
    // Cast for compatibility with Express' json typing
    return (originalResJson as (body?: unknown) => Response).call(
      res,
      bodyJson
    );
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: unknown, _req: Request, res: Response, _next: NextFunction) => {
    const status =
      typeof err === "object" && err !== null && "status" in err
        ? Number(
            (err as { status?: unknown; statusCode?: unknown }).status ??
              (err as { statusCode?: unknown }).statusCode ??
              500
          )
        : 500;
    const message =
      typeof err === "object" && err !== null && "message" in err
        ? String(
            (err as { message?: unknown }).message || "Internal Server Error"
          )
        : "Internal Server Error";

    res.status(status).json({ message });
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = process.env.PORT || 5000;
  server.listen(
    {
      port,
      host: "localhost",
    },
    () => {
      log(`serving on port ${port}`);
    }
  );
})();
