import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useState } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import CookieNotice from "@/components/CookieNotice";
import ailexIcon from "@/assets/ailex-icon.png";

export default function TexasPiPage() {
  // Set page title
  useEffect(() => {
    document.title = "AiLex - AI Legal Assistant for Texas PI Lawyers";
  }, []);

  // State for expandable items
  const [expandedItems, setExpandedItems] = useState<number[]>([]);

  // State for billing toggle
  const [isAnnual, setIsAnnual] = useState(false);

  // Get Stripe checkout URL from environment
  const stripeCheckoutUrl = import.meta.env.VITE_STRIPE_CHECKOUT_URL;

  return (
    // <PreviewPasswordGate>
    <div className="relative overflow-x-hidden bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="relative min-h-screen overflow-hidden">
        {/* Cloudy Gradient Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-[#f5f7fa] via-[#e8f4fd] to-[#e1f5fe]"></div>
          <div className="absolute inset-0 opacity-40">
            <div
              className="absolute top-0 left-0 w-full h-full transform -translate-x-1/4 -translate-y-1/4 scale-150"
              style={{
                background:
                  "radial-gradient(circle, rgba(30, 174, 219, 0.15) 0%, transparent 50%)",
              }}
            ></div>
            <div
              className="absolute top-1/4 right-0 w-3/4 h-3/4 transform translate-x-1/4 scale-125"
              style={{
                background:
                  "radial-gradient(circle, rgba(60, 130, 246, 0.12) 0%, transparent 60%)",
              }}
            ></div>
          </div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-6 pt-32 pb-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Content */}
            <div>
              {/* Eyebrow with Moving Ball */}
              <motion.div
                className="flex justify-center mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.05 }}
              >
                <motion.div
                  className="flex items-center gap-2 text-sm font-medium text-gray-600 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50 shadow-sm"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <motion.div
                    className="w-2 h-2 bg-gradient-to-r from-[#3c82f6] to-[#1eaedb] rounded-full"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.8, 1, 0.8],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                  Built for Texas Personal Injury Law
                </motion.div>
              </motion.div>

              <motion.h1
                className="text-5xl lg:text-6xl font-bold text-[#0c1c2d] mb-6 leading-tight"
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
              >
                Your AI Legal Assistant for Personal Injury
              </motion.h1>

              <motion.h2
                className="text-3xl lg:text-4xl font-bold text-[#0c1c2d] mb-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
              >
                Built for{" "}
                <span className="bg-gradient-to-r from-[#1eaedb] to-[#3c82f6] bg-clip-text text-transparent">
                  Texas Solo Attorneys
                </span>
              </motion.h2>

              <motion.p
                className="text-xl text-slate-600 mb-10 leading-relaxed"
                initial={{ opacity: 0, y: 25 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
              >
                Upload medical docs, draft insurer demands, calculate damages,
                and track deadlines — all in one place.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
              >
                {stripeCheckoutUrl ? (
                  <a
                    href={stripeCheckoutUrl}
                    className="inline-flex items-center gap-3 bg-gradient-to-r from-[#1eaedb] to-[#3c82f6] hover:from-[#3c82f6] hover:to-[#1eaedb] text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    Start Free Trial
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 8l4 4m0 0l-4 4m4-4H3"
                      />
                    </svg>
                  </a>
                ) : (
                  <button
                    disabled
                    className="inline-flex items-center gap-3 bg-gray-300 text-gray-500 px-8 py-4 rounded-xl font-semibold text-lg cursor-not-allowed"
                  >
                    Preview config missing
                  </button>
                )}
              </motion.div>

              {/* Tick Marks - Benefits */}
              <motion.div
                className="flex items-center justify-center gap-8 mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.9 }}
              >
                {/* Start immediately */}
                <motion.div
                  className="flex items-center gap-2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 1.0 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <motion.div
                    className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{
                      duration: 0.4,
                      delay: 1.1,
                      type: "spring",
                      stiffness: 200,
                    }}
                  >
                    <svg
                      className="w-3 h-3 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={3}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </motion.div>
                  <span className="text-sm font-medium text-gray-700">
                    Start immediately
                  </span>
                </motion.div>

                {/* No setup needed */}
                <motion.div
                  className="flex items-center gap-2"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.1 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <motion.div
                    className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{
                      duration: 0.4,
                      delay: 1.2,
                      type: "spring",
                      stiffness: 200,
                    }}
                  >
                    <svg
                      className="w-3 h-3 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={3}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </motion.div>
                  <span className="text-sm font-medium text-gray-700">
                    No setup needed
                  </span>
                </motion.div>

                {/* No learning curve */}
                <motion.div
                  className="flex items-center gap-2"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 1.2 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <motion.div
                    className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{
                      duration: 0.4,
                      delay: 1.3,
                      type: "spring",
                      stiffness: 200,
                    }}
                  >
                    <svg
                      className="w-3 h-3 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={3}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </motion.div>
                  <span className="text-sm font-medium text-gray-700">
                    No learning curve
                  </span>
                </motion.div>
              </motion.div>
            </div>

            {/* Right Column - Sophisticated Dashboard Mockup */}
            <motion.div
              className="relative perspective-1000"
              initial={{ opacity: 0, x: 40, rotateY: 15, scale: 0.9 }}
              animate={{
                opacity: 1,
                x: 0,
                rotateY: 0,
                scale: 1,
                y: [0, -8, 0],
              }}
              transition={{
                duration: 1,
                delay: 0.4,
                y: {
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                },
              }}
              whileHover={{
                y: -15,
                rotateY: -8,
                scale: 1.02,
                x: [0, -5, 5, -5, 5, 0],
                transition: {
                  duration: 0.4,
                  ease: "easeOut",
                  x: {
                    duration: 0.6,
                    ease: "easeInOut",
                  },
                },
              }}
            >
              {/* Multiple background blur layers for depth */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100/40 to-purple-100/40 rounded-3xl blur-3xl transform scale-110 animate-pulse"></div>
              <div className="absolute inset-0 bg-gradient-to-tr from-cyan-50/30 to-blue-50/30 rounded-3xl blur-2xl transform scale-105"></div>

              {/* Main dashboard container with enhanced 3D effects */}
              <div
                className="relative bg-gradient-to-br from-white via-white to-gray-50/50 backdrop-blur-md rounded-3xl overflow-hidden border border-white/30 max-w-md mx-auto transform-gpu"
                style={{
                  boxShadow: `
                         0 35px 60px -12px rgba(0, 0, 0, 0.3),
                         0 20px 25px -5px rgba(0, 0, 0, 0.1),
                         0 0 0 1px rgba(255, 255, 255, 0.1),
                         inset 0 1px 0 rgba(255, 255, 255, 0.2),
                         inset 0 -1px 0 rgba(0, 0, 0, 0.05)
                       `,
                }}
              >
                {/* Dashboard Header */}
                <div className="bg-gradient-to-r from-white to-gray-50/30 p-6 border-b border-gray-100/50 backdrop-blur-sm">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-[#3c82f6] to-[#2563eb] rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 transform hover:scale-105 transition-transform">
                        <span className="text-white font-bold text-lg drop-shadow-sm">
                          AI
                        </span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 drop-shadow-sm">
                          AiLex Dashboard
                        </h3>
                        <p className="text-sm text-gray-500">
                          Firm: Rodriguez & Associates
                        </p>
                      </div>
                    </div>
                    <div className="bg-gradient-to-r from-[#b8ff5c] to-[#a3e635] text-black px-3 py-1 rounded-full text-sm font-medium shadow-md shadow-green-500/20 transform hover:scale-105 transition-transform">
                      Ready
                    </div>
                  </div>
                </div>

                {/* Active Matters Section */}
                <div className="p-6 border-b border-gray-100/50 bg-gradient-to-br from-white to-blue-50/20">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-sm text-gray-500 uppercase tracking-wide font-medium">
                        ACTIVE MATTERS
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-3xl font-bold text-[#3c82f6] drop-shadow-sm">
                          18
                        </span>
                        <span className="text-sm text-green-500 flex items-center gap-1 bg-green-50 px-2 py-1 rounded-full">
                          <svg
                            className="w-3 h-3 drop-shadow-sm"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          +3 this week
                        </span>
                      </div>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl flex items-center justify-center shadow-md shadow-blue-500/10 transform hover:scale-105 transition-transform">
                      <svg
                        className="w-6 h-6 text-[#3c82f6] drop-shadow-sm"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <div className="h-1.5 bg-gradient-to-r from-[#3c82f6] to-[#2563eb] rounded-full flex-1 shadow-sm"></div>
                    <div className="h-1.5 bg-gray-200 rounded-full w-8 shadow-sm"></div>
                    <div className="h-1.5 bg-gray-200 rounded-full w-4 shadow-sm"></div>
                  </div>
                </div>

                {/* Recent Activity Section */}
                <div className="p-6 border-b border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <p className="text-sm text-gray-500 uppercase tracking-wide">
                      RECENT ACTIVITY
                    </p>
                    <button className="text-sm text-[#3c82f6] hover:text-blue-700">
                      See all
                    </button>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg
                          className="w-4 h-4 text-[#3c82f6]"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">
                          Medical Records Analysis
                        </p>
                        <p className="text-sm text-gray-500">
                          Martinez v. State Farm
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg
                          className="w-4 h-4 text-green-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                          />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">
                          Demand Letter Sent
                        </p>
                        <p className="text-sm text-gray-500">
                          Johnson Auto Accident
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Upcoming Section */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <p className="text-sm text-gray-500 uppercase tracking-wide">
                      UPCOMING
                    </p>
                    <button className="text-sm text-[#3c82f6] hover:text-blue-700">
                      Schedule
                    </button>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">
                          Statute Deadline
                        </p>
                        <p className="text-sm text-gray-500">
                          May 15 - Martinez Case
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">Mediation</p>
                        <p className="text-sm text-gray-500">
                          May 20 - Johnson
                        </p>
                      </div>
                    </div>
                  </div>

                  <button className="w-full mt-6 bg-[#3c82f6] text-white py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors">
                    Open Dashboard
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Video Introduction Section */}
      <section className="py-20 bg-gradient-to-b from-blue-50 via-white to-blue-50/30 relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 opacity-40">
          <div
            className="absolute top-1/4 left-1/4 w-96 h-96 transform -translate-x-1/2 -translate-y-1/2 scale-150"
            style={{
              background:
                "radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, rgba(147, 197, 253, 0.1) 40%, transparent 70%)",
            }}
          ></div>
          <div
            className="absolute bottom-1/4 right-0 w-80 h-80 transform translate-x-1/3 translate-y-1/4 scale-125"
            style={{
              background:
                "radial-gradient(circle, rgba(96, 165, 250, 0.12) 0%, rgba(191, 219, 254, 0.08) 50%, transparent 80%)",
            }}
          ></div>
        </div>

        <div className="max-w-4xl mx-auto px-6 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Headline */}
            <motion.h2
              className="text-4xl lg:text-5xl font-bold text-[#0c1c2d] mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              Meet AiLex in 15 Seconds
            </motion.h2>

            {/* Subheading */}
            <motion.p
              className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              A quick introduction from our AiLex avatar — built for Texas PI
              solos.
            </motion.p>

            {/* Video Block */}
            <motion.div
              className="mb-8"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="relative max-w-2xl mx-auto">
                {/* YouTube Video Container */}
                <div className="relative bg-gradient-to-br from-[#052f5f] to-[#0c1c2d] rounded-3xl p-8 shadow-2xl">
                  {/* Video Placeholder */}
                  <div className="relative aspect-video rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg
                          className="w-8 h-8 text-gray-500"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M8 5v14l11-7z" />
                        </svg>
                      </div>
                      <p className="text-gray-600 font-medium">
                        Video Coming Soon
                      </p>
                      <p className="text-gray-500 text-sm mt-1">
                        15 second overview
                      </p>
                    </div>
                  </div>

                  {/* Caption */}
                  <p className="text-white/70 text-sm mt-4 text-center">
                    15 sec overview
                  </p>
                </div>
              </div>
            </motion.div>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <motion.a
                href="/login"
                className="inline-flex items-center gap-2 bg-[#3B66F8] text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:bg-blue-700 transition-all duration-300"
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 20px 40px -10px rgba(59, 102, 248, 0.4)",
                  transition: { duration: 0.2 },
                }}
                whileTap={{ scale: 0.98 }}
              >
                Start Free Trial
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 8l4 4m0 0l-4 4m4-4H3"
                  />
                </svg>
              </motion.a>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Pain Points Section */}
      <section className="py-20 bg-gradient-to-b from-[#0c1c2d] to-[#052f5f]">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Eyebrow for Pain Points */}
            <motion.div
              className="flex justify-center mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="flex items-center gap-2 text-sm font-medium text-white/80 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full border border-white/20 shadow-sm"
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="w-2 h-2 bg-gradient-to-r from-[#ff6b6b] to-[#ffa726] rounded-full"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                Challenges
              </motion.div>
            </motion.div>

            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Where Texas PI Solos Lose Time, Money, and Energy
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Medical Records Card */}
            <motion.div
              className="bg-white border border-gray-200 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3, ease: "easeOut" },
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="bg-red-50 p-3 rounded-2xl">
                  <svg
                    className="w-6 h-6 text-red-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <span className="text-red-500 text-sm font-medium bg-red-50 px-3 py-1 rounded-full">
                  -73%
                </span>
              </div>

              <div className="mb-4">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  8.5 hrs
                </div>
                <div className="text-sm text-gray-500 mb-3">
                  Average per case
                </div>

                {/* Mini chart */}
                <div className="flex items-end space-x-1 h-12">
                  {[40, 65, 45, 80, 70, 90, 85].map((height, i) => (
                    <div
                      key={i}
                      className="bg-red-200 rounded-t flex-1"
                      style={{ height: `${height}%` }}
                    />
                  ))}
                </div>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Medical Records
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Unstructured records require manual review and organization
              </p>
            </motion.div>

            {/* Demand Letters Card */}
            <motion.div
              className="bg-white border border-gray-200 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3, ease: "easeOut" },
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="bg-orange-50 p-3 rounded-2xl">
                  <svg
                    className="w-6 h-6 text-orange-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                  <span className="text-orange-500 text-sm font-medium">
                    Active
                  </span>
                </div>
              </div>

              <div className="mb-4">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  12.3 hrs
                </div>
                <div className="text-sm text-gray-500 mb-3">
                  Per demand letter
                </div>

                {/* Progress rings */}
                <div className="flex space-x-3">
                  <div className="relative w-12 h-12">
                    <svg
                      className="w-12 h-12 transform -rotate-90"
                      viewBox="0 0 36 36"
                    >
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#f3f4f6"
                        strokeWidth="2"
                      />
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#f97316"
                        strokeWidth="2"
                        strokeDasharray="75, 100"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-semibold text-gray-700">
                      75%
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 flex flex-col justify-center">
                    <div>Research</div>
                    <div>& Writing</div>
                  </div>
                </div>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Demand Letters
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Complex legal writing requires extensive research and precision
              </p>
            </motion.div>

            {/* Missing Damages Card */}
            <motion.div
              className="bg-white border border-gray-200 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3, ease: "easeOut" },
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="bg-yellow-50 p-3 rounded-2xl">
                  <svg
                    className="w-6 h-6 text-yellow-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                    />
                  </svg>
                </div>
                <span className="text-yellow-600 text-sm font-medium bg-yellow-50 px-3 py-1 rounded-full">
                  Risk
                </span>
              </div>

              <div className="mb-4">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  $47K
                </div>
                <div className="text-sm text-gray-500 mb-3">
                  Avg. missed damages
                </div>

                {/* Stacked bars */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="text-xs text-gray-500 w-16">Medical</div>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full"
                        style={{ width: "85%" }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-700 w-8">85%</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-xs text-gray-500 w-16">Lost wages</div>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full"
                        style={{ width: "62%" }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-700 w-8">62%</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-xs text-gray-500 w-16">Pain</div>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full"
                        style={{ width: "43%" }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-700 w-8">43%</div>
                  </div>
                </div>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Damage Calculations
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Manual calculations often miss significant compensation
                categories
              </p>
            </motion.div>

            {/* Deadline Stress Card */}
            <motion.div
              className="bg-white border border-gray-200 rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3, ease: "easeOut" },
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="bg-purple-50 p-3 rounded-2xl">
                  <svg
                    className="w-6 h-6 text-purple-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                </div>
              </div>

              <div className="mb-4">
                <div className="text-2xl font-bold text-gray-900 mb-1">73%</div>
                <div className="text-sm text-gray-500 mb-3">Stress level</div>

                {/* Stress meter */}
                <div className="relative">
                  <div className="flex justify-between text-xs text-gray-400 mb-1">
                    <span>Low</span>
                    <span>High</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div className="bg-gradient-to-r from-green-400 via-yellow-400 to-red-400 h-3 rounded-full relative">
                      <div className="absolute right-[27%] top-0 w-1 h-3 bg-white rounded-full shadow-sm"></div>
                    </div>
                  </div>

                  {/* Task indicators */}
                  <div className="mt-3 flex justify-between text-xs">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                      <span className="text-gray-600">Overdue: 3</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                      <span className="text-gray-600">Due today: 7</span>
                    </div>
                  </div>
                </div>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Case Management
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                Multiple deadlines and manual tracking create constant pressure
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Solution / Core Features Section */}
      <section
        id="features"
        className="py-20 bg-gradient-to-b from-[#f5f7fa] to-white"
      >
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Eyebrow for Features */}
            <motion.div
              className="flex justify-center mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="flex items-center gap-2 text-sm font-medium text-gray-600 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50 shadow-sm"
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="w-2 h-2 bg-gradient-to-r from-[#10b981] to-[#34d399] rounded-full"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                Features
              </motion.div>
            </motion.div>

            <h2 className="text-4xl lg:text-5xl font-bold text-[#0c1c2d] mb-6">
              How AiLex helps you win more time and cases
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Medical Command Center Card */}
            <motion.div
              className="group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100/50 h-full overflow-hidden relative"
                animate={{
                  y: [0, -8, 0],
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0,
                }}
                whileHover={{
                  y: -12,
                  scale: 1.02,
                  transition: { duration: 0.3 },
                }}
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        Medical Records
                      </h3>
                      <p className="text-sm text-gray-500">
                        Processing efficiency
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">94%</div>
                    <div className="text-xs text-green-600 font-medium">
                      +12%
                    </div>
                  </div>
                </div>

                {/* Chart Area */}
                <div className="mb-6 relative h-32">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl"></div>
                  <svg
                    className="absolute inset-0 w-full h-full"
                    viewBox="0 0 300 120"
                  >
                    <defs>
                      <linearGradient
                        id="medicalGradient"
                        x1="0%"
                        y1="0%"
                        x2="0%"
                        y2="100%"
                      >
                        <stop
                          offset="0%"
                          stopColor="#3b82f6"
                          stopOpacity="0.3"
                        />
                        <stop
                          offset="100%"
                          stopColor="#3b82f6"
                          stopOpacity="0.1"
                        />
                      </linearGradient>
                    </defs>
                    <path
                      d="M20,80 Q60,60 100,70 T180,50 Q220,45 260,40 L280,35"
                      stroke="#3b82f6"
                      strokeWidth="3"
                      fill="none"
                      className="drop-shadow-sm"
                    />
                    <path
                      d="M20,80 Q60,60 100,70 T180,50 Q220,45 260,40 L280,35 L280,100 L20,100 Z"
                      fill="url(#medicalGradient)"
                    />
                    {/* Data points */}
                    <circle
                      cx="100"
                      cy="70"
                      r="4"
                      fill="#3b82f6"
                      className="drop-shadow-sm"
                    />
                    <circle
                      cx="180"
                      cy="50"
                      r="4"
                      fill="#3b82f6"
                      className="drop-shadow-sm"
                    />
                    <circle
                      cx="260"
                      cy="40"
                      r="4"
                      fill="#3b82f6"
                      className="drop-shadow-sm"
                    />
                  </svg>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold text-gray-900">2.3h</div>
                    <div className="text-xs text-gray-500">Avg processing</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">847</div>
                    <div className="text-xs text-gray-500">
                      Records processed
                    </div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">99.2%</div>
                    <div className="text-xs text-gray-500">Accuracy rate</div>
                  </div>
                </div>

                <p className="text-gray-600 mt-4 text-sm leading-relaxed">
                  AI-powered extraction from complex medical records with
                  structured data output
                </p>
              </motion.div>
            </motion.div>

            {/* Insurance Battle Station Card */}
            <motion.div
              className="group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100/50 h-full overflow-hidden relative"
                animate={{
                  y: [0, -6, 0],
                }}
                transition={{
                  duration: 7,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1,
                }}
                whileHover={{
                  y: -12,
                  scale: 1.02,
                  transition: { duration: 0.3 },
                }}
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-slate-700 to-slate-800 rounded-2xl flex items-center justify-center">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        Demand Letters
                      </h3>
                      <p className="text-sm text-gray-500">Success metrics</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      $2.4M
                    </div>
                    <div className="text-xs text-green-600 font-medium">
                      +18%
                    </div>
                  </div>
                </div>

                {/* Progress Bars */}
                <div className="mb-6 space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-600">Settlement Rate</span>
                      <span className="font-medium text-gray-900">87%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-slate-600 to-slate-700 h-2 rounded-full"
                        initial={{ width: 0 }}
                        whileInView={{ width: "87%" }}
                        transition={{ duration: 1.5, delay: 0.5 }}
                        viewport={{ once: true }}
                      ></motion.div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-600">Response Time</span>
                      <span className="font-medium text-gray-900">92%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full"
                        initial={{ width: 0 }}
                        whileInView={{ width: "92%" }}
                        transition={{ duration: 1.5, delay: 0.7 }}
                        viewport={{ once: true }}
                      ></motion.div>
                    </div>
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold text-gray-900">12min</div>
                    <div className="text-xs text-gray-500">Avg draft time</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">156</div>
                    <div className="text-xs text-gray-500">Letters sent</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">87%</div>
                    <div className="text-xs text-gray-500">Success rate</div>
                  </div>
                </div>

                <p className="text-gray-600 mt-4 text-sm leading-relaxed">
                  Professional demand letters drafted in minutes with
                  Texas-specific precedents
                </p>
              </motion.div>
            </motion.div>

            {/* Damages Calculator Card */}
            <motion.div
              className="group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100/50 h-full overflow-hidden relative"
                animate={{
                  y: [0, -10, 0],
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 2,
                }}
                whileHover={{
                  y: -12,
                  scale: 1.02,
                  transition: { duration: 0.3 },
                }}
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        Damages Calculator
                      </h3>
                      <p className="text-sm text-gray-500">
                        Calculation accuracy
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      $847K
                    </div>
                    <div className="text-xs text-green-600 font-medium">
                      +24%
                    </div>
                  </div>
                </div>

                {/* Pie Chart */}
                <div className="mb-6 flex justify-center">
                  <div className="relative w-24 h-24">
                    <svg
                      className="w-24 h-24 transform -rotate-90"
                      viewBox="0 0 100 100"
                    >
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#f3f4f6"
                        strokeWidth="8"
                        fill="none"
                      />
                      <motion.circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#10b981"
                        strokeWidth="8"
                        fill="none"
                        strokeLinecap="round"
                        strokeDasharray="251.2"
                        initial={{ strokeDashoffset: 251.2 }}
                        whileInView={{ strokeDashoffset: 50.24 }}
                        transition={{ duration: 2, delay: 0.5 }}
                        viewport={{ once: true }}
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-lg font-bold text-gray-900">
                        80%
                      </span>
                    </div>
                  </div>
                </div>

                {/* Breakdown */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">Medical</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      $340K
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">Lost wages</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      $285K
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">
                        Pain & suffering
                      </span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      $222K
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 text-sm leading-relaxed">
                  Fast, accurate breakdowns with Texas precedents and case law
                  integration
                </p>
              </motion.div>
            </motion.div>

            {/* Practice Management Card */}
            <motion.div
              className="group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100/50 h-full overflow-hidden relative"
                animate={{
                  y: [0, -7, 0],
                }}
                transition={{
                  duration: 9,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 3,
                }}
                whileHover={{
                  y: -12,
                  scale: 1.02,
                  transition: { duration: 0.3 },
                }}
              >
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                        />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">
                        Practice Management
                      </h3>
                      <p className="text-sm text-gray-500">
                        Workflow efficiency
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">96%</div>
                    <div className="text-xs text-green-600 font-medium">
                      +8%
                    </div>
                  </div>
                </div>

                {/* Task Status */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-gray-600">Today's Tasks</span>
                    <span className="text-sm font-medium text-gray-900">
                      12 of 15
                    </span>
                  </div>
                  <div className="space-y-2">
                    {[
                      { task: "Review medical records", status: "completed" },
                      { task: "Draft demand letter", status: "completed" },
                      { task: "Client consultation", status: "in-progress" },
                      { task: "Court filing deadline", status: "pending" },
                    ].map((item, idx) => (
                      <div key={idx} className="flex items-center gap-3">
                        <div
                          className={`w-2 h-2 rounded-full ${
                            item.status === "completed"
                              ? "bg-green-500"
                              : item.status === "in-progress"
                                ? "bg-yellow-500"
                                : "bg-gray-300"
                          }`}
                        ></div>
                        <span
                          className={`text-xs ${
                            item.status === "completed"
                              ? "text-gray-500 line-through"
                              : "text-gray-700"
                          }`}
                        >
                          {item.task}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold text-gray-900">23</div>
                    <div className="text-xs text-gray-500">Active cases</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">5</div>
                    <div className="text-xs text-gray-500">Due today</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900">98%</div>
                    <div className="text-xs text-gray-500">On-time rate</div>
                  </div>
                </div>

                <p className="text-gray-600 mt-4 text-sm leading-relaxed">
                  Tasks, deadlines, and matter management integrated in one
                  platform
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Why Choose AiLex Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            className="bg-gray-50 border border-gray-200 rounded-2xl p-8"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            whileHover={{
              y: -5,
              boxShadow:
                "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
              transition: { duration: 0.3 },
            }}
          >
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Left Column - Content */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h2 className="text-4xl lg:text-5xl font-bold text-[#0c1c2d] mb-8">
                  Built only for Texas PI attorneys
                </h2>

                <div className="space-y-4">
                  {[
                    {
                      title: "Focused on Texas PI workflows",
                      description:
                        "Built specifically for personal injury law practice in Texas with state-specific forms and procedures.",
                      icon: (
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z"
                          />
                        </svg>
                      ),
                      iconColor: "text-blue-600",
                      bgColor: "bg-blue-50",
                    },
                    {
                      title: "Free trial, no setup needed",
                      description:
                        "Start immediately with zero configuration. No credit card required, no lengthy onboarding process.",
                      icon: (
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"
                          />
                        </svg>
                      ),
                      iconColor: "text-emerald-600",
                      bgColor: "bg-emerald-50",
                    },
                    {
                      title: "Solo-friendly — all in one platform",
                      description:
                        "Everything you need in one place: case management, client communication, document drafting, and billing.",
                      icon: (
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z"
                          />
                        </svg>
                      ),
                      iconColor: "text-purple-600",
                      bgColor: "bg-purple-50",
                    },
                  ].map((benefit, index) => {
                    const isExpanded = expandedItems.includes(index);

                    return (
                      <motion.div
                        key={index}
                        className="group"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 }}
                        viewport={{ once: true }}
                      >
                        <div className="rounded-2xl border border-gray-200 bg-white hover:bg-gray-50 transition-all duration-300 hover:shadow-md overflow-hidden">
                          <div
                            className="flex items-center justify-between p-4 cursor-pointer"
                            onClick={() => {
                              setExpandedItems((prev) =>
                                isExpanded
                                  ? prev.filter((i) => i !== index)
                                  : [...prev, index]
                              );
                            }}
                          >
                            <div className="flex items-center gap-4">
                              <div
                                className={`w-12 h-12 ${benefit.bgColor} rounded-2xl flex items-center justify-center ${benefit.iconColor} border border-gray-100`}
                              >
                                {benefit.icon}
                              </div>
                              <h4 className="font-semibold text-gray-900 text-lg group-hover:text-gray-700 transition-colors">
                                {benefit.title}
                              </h4>
                            </div>
                            <motion.div
                              className="text-gray-400 group-hover:text-gray-600 transition-colors"
                              animate={{ rotate: isExpanded ? 180 : 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <svg
                                className="w-5 h-5"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 9l-7 7-7-7"
                                />
                              </svg>
                            </motion.div>
                          </div>

                          <AnimatePresence>
                            {isExpanded && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: "auto", opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{
                                  duration: 0.3,
                                  ease: "easeInOut",
                                }}
                                className="overflow-hidden"
                              >
                                <div className="px-4 pb-4 pl-20">
                                  <p className="text-gray-600 leading-relaxed">
                                    {benefit.description}
                                  </p>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>

              {/* Right Column - Visual */}
              <motion.div
                className="relative"
                initial={{ opacity: 0, x: 40 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                  {/* Header with metrics */}
                  <div className="flex justify-between items-start mb-8">
                    <div>
                      <div className="text-sm text-gray-500 mb-1">
                        Texas Compliance
                      </div>
                      <div className="text-3xl font-bold text-gray-900">
                        100%
                      </div>
                      <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full inline-block mt-1">
                        +24%
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500 mb-1">
                        Active features
                      </div>
                      <div className="text-2xl font-bold text-gray-900">12</div>
                    </div>
                  </div>

                  {/* Visual chart representation */}
                  <div className="mb-8">
                    <div className="flex items-end justify-between h-16 mb-2">
                      <div
                        className="w-3 bg-blue-200 rounded-t"
                        style={{ height: "40%" }}
                      ></div>
                      <div
                        className="w-3 bg-blue-300 rounded-t"
                        style={{ height: "60%" }}
                      ></div>
                      <div
                        className="w-3 bg-blue-400 rounded-t"
                        style={{ height: "80%" }}
                      ></div>
                      <div
                        className="w-3 bg-blue-500 rounded-t"
                        style={{ height: "100%" }}
                      ></div>
                      <div
                        className="w-3 bg-blue-600 rounded-t"
                        style={{ height: "90%" }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-400 text-center">
                      Compliance tracking over time
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-gray-900 mb-6">
                    Texas-Specific Features
                  </h3>

                  {/* Feature tags in Framer style */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl border border-gray-100">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="font-medium text-gray-900 text-sm">
                          Texas Statute of Limitations
                        </span>
                      </div>
                      <div className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                        Auto-track
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl border border-gray-100">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="font-medium text-gray-900 text-sm">
                          Medical Records Format
                        </span>
                      </div>
                      <div className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                        Native
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl border border-gray-100">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span className="font-medium text-gray-900 text-sm">
                          Comparative Negligence
                        </span>
                      </div>
                      <div className="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded-full">
                        Built-in
                      </div>
                    </div>
                  </div>

                  <p className="text-gray-600 mt-6 text-sm leading-relaxed">
                    Comprehensive Texas law integration with real-time
                    compliance monitoring and automated deadline management.
                  </p>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Section */}
      <section
        id="pricing"
        className="py-20 bg-gradient-to-b from-blue-50 via-white to-blue-50/30 relative overflow-hidden"
      >
        {/* Cloudy Background Effects */}
        <div className="absolute inset-0 opacity-40">
          {/* Light blue clouds */}
          <div
            className="absolute top-0 left-1/4 w-96 h-96 transform -translate-x-1/2 -translate-y-1/2 scale-150"
            style={{
              background:
                "radial-gradient(circle, rgba(59, 130, 246, 0.15) 0%, rgba(147, 197, 253, 0.1) 40%, transparent 70%)",
            }}
          ></div>
          <div
            className="absolute top-1/3 right-0 w-80 h-80 transform translate-x-1/3 -translate-y-1/2 scale-125"
            style={{
              background:
                "radial-gradient(circle, rgba(96, 165, 250, 0.12) 0%, rgba(191, 219, 254, 0.08) 50%, transparent 80%)",
            }}
          ></div>
          {/* Animated Lime Green Clouds */}
          <motion.div
            className="absolute bottom-1/4 left-0 w-80 h-80 transform -translate-x-1/2 translate-y-1/4"
            style={{
              background:
                "radial-gradient(circle, rgba(184, 255, 92, 0.35) 0%, rgba(163, 230, 53, 0.20) 40%, transparent 70%)",
            }}
            animate={{
              x: [0, 30, 0],
              y: [0, -20, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          <motion.div
            className="absolute top-2/3 right-1/4 w-96 h-96 transform translate-x-1/2"
            style={{
              background:
                "radial-gradient(circle, rgba(184, 255, 92, 0.30) 0%, rgba(132, 204, 22, 0.15) 50%, transparent 80%)",
            }}
            animate={{
              x: [0, -25, 0],
              y: [0, 15, 0],
              scale: [1, 0.9, 1],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          />

          {/* Additional Floating Lime Clouds */}
          <motion.div
            className="absolute top-1/4 left-1/3 w-64 h-64 transform -translate-x-1/2"
            style={{
              background:
                "radial-gradient(circle, rgba(184, 255, 92, 0.40) 0%, rgba(101, 163, 13, 0.25) 60%, transparent 80%)",
            }}
            animate={{
              x: [0, 20, 0],
              y: [0, -15, 0],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1,
            }}
          />

          <motion.div
            className="absolute bottom-10 right-10 w-56 h-56"
            style={{
              background:
                "radial-gradient(circle, rgba(184, 255, 92, 0.35) 0%, rgba(163, 230, 53, 0.20) 50%, transparent 70%)",
            }}
            animate={{
              x: [0, -15, 0],
              y: [0, 10, 0],
              scale: [1, 1.15, 1],
            }}
            transition={{
              duration: 7,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 3,
            }}
          />

          {/* Central Floating Lime Cloud */}
          <motion.div
            className="absolute top-1/2 left-1/2 w-72 h-72 transform -translate-x-1/2 -translate-y-1/2"
            style={{
              background:
                "radial-gradient(circle, rgba(184, 255, 92, 0.25) 0%, rgba(163, 230, 53, 0.15) 60%, transparent 80%)",
            }}
            animate={{
              x: [0, 25, 0],
              y: [0, -30, 0],
              scale: [1, 0.85, 1],
            }}
            transition={{
              duration: 9,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4,
            }}
          />

          {/* Additional light blue accents */}
          <div
            className="absolute bottom-0 center w-full h-32 transform translate-y-1/2"
            style={{
              background:
                "linear-gradient(to top, rgba(219, 234, 254, 0.3) 0%, transparent 100%)",
            }}
          ></div>
        </div>

        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Eyebrow for Pricing */}
            <motion.div
              className="flex justify-center mb-6"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="flex items-center gap-2 text-sm font-medium text-gray-600 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50 shadow-sm"
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="w-2 h-2 bg-gradient-to-r from-[#8b5cf6] to-[#a855f7] rounded-full"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                Pricing
              </motion.div>
            </motion.div>

            <motion.h2
              className="text-4xl lg:text-5xl font-bold text-[#0c1c2d] mb-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              Simple, predictable pricing for{" "}
              <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                Texas PI lawyers
              </span>
            </motion.h2>

            {/* Monthly/Annual Toggle */}
            <motion.div
              className="flex items-center justify-center mb-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-2 border border-gray-200/50 shadow-sm">
                <div className="flex items-center">
                  <button
                    onClick={() => setIsAnnual(false)}
                    className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                      !isAnnual
                        ? "bg-blue-500 text-white shadow-md"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    Monthly
                  </button>
                  <button
                    onClick={() => setIsAnnual(true)}
                    className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 relative ${
                      isAnnual
                        ? "bg-blue-500 text-white shadow-md"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    Annual
                    <span className="absolute -top-1 -right-1 bg-[#b8ff5c] text-black text-xs px-2 py-0.5 rounded-full font-semibold">
                      -15%
                    </span>
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Two-Card Layout: Pricing + Contact */}
          <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {/* Solo Plan Card - Featured */}
            <motion.div
              className="relative group"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3, ease: "easeOut" },
              }}
            >
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl shadow-xl p-8 border-2 border-blue-400 h-full relative overflow-hidden transition-all duration-300 group-hover:shadow-2xl group-hover:border-blue-300">
                {/* Subtle pattern overlay */}
                <div className="absolute inset-0 opacity-10 transition-opacity duration-300 group-hover:opacity-20">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full transform translate-x-16 -translate-y-16 transition-transform duration-500 group-hover:scale-110 group-hover:translate-x-12"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full transform -translate-x-12 translate-y-12 transition-transform duration-700 group-hover:scale-125 group-hover:-translate-x-8"></div>
                </div>

                {/* Header */}
                <div className="mb-8 text-center relative z-10">
                  <h3 className="text-2xl font-bold text-white mb-4">
                    Solo Plan
                  </h3>

                  {/* Per-User Value Badge */}
                  <motion.div
                    className="inline-flex items-center gap-2 bg-white text-blue-600 px-5 py-2.5 rounded-full text-sm font-bold mb-4 shadow-xl border-2 border-white/20"
                    initial={{ scale: 0, opacity: 0 }}
                    whileInView={{ scale: 1, opacity: 1 }}
                    transition={{
                      duration: 0.6,
                      delay: 0.4,
                      type: "spring",
                      stiffness: 200,
                    }}
                    whileHover={{
                      scale: 1.05,
                      boxShadow: "0 15px 30px rgba(255, 255, 255, 0.3)",
                      y: -2,
                    }}
                    viewport={{ once: true }}
                  >
                    <motion.div
                      className="w-2 h-2 bg-lime-400 rounded-full"
                      animate={{
                        scale: [1, 1.4, 1],
                        opacity: [0.8, 1, 0.8],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    />
                    <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent font-extrabold">
                      Only ~${isAnnual ? "42" : "50"}/user
                    </span>
                    <motion.div
                      className="w-2 h-2 bg-lime-400 rounded-full"
                      animate={{
                        scale: [1, 1.4, 1],
                        opacity: [0.8, 1, 0.8],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 1,
                      }}
                    />
                  </motion.div>

                  <div className="mb-2">
                    {isAnnual ? (
                      <>
                        <div className="flex items-center justify-center gap-2 mb-1">
                          <span className="text-2xl text-blue-200 line-through">
                            $99
                          </span>
                          <span className="text-5xl font-bold text-white">
                            $84
                          </span>
                          <span className="text-blue-100 text-lg">/month</span>
                        </div>
                        <p className="text-green-300 text-sm font-medium">
                          Save $180/year with annual billing
                        </p>
                      </>
                    ) : (
                      <>
                        <span className="text-5xl font-bold text-white">
                          $99
                        </span>
                        <span className="text-blue-100 text-lg">/month</span>
                      </>
                    )}
                  </div>
                  <p className="text-white font-bold text-lg mb-1">
                    Includes 2 users
                  </p>
                  <p className="text-blue-200 text-sm">
                    billed {isAnnual ? "annually" : "monthly"}
                  </p>
                </div>

                {/* CTA Button */}
                <div className="mb-8 relative z-10">
                  {stripeCheckoutUrl ? (
                    <motion.a
                      href={stripeCheckoutUrl}
                      className="w-full bg-gradient-to-r from-white to-gray-50 hover:from-gray-50 hover:to-white text-blue-600 py-5 px-6 rounded-2xl font-bold text-center block transition-all duration-200 shadow-2xl border-2 border-white/30 text-lg"
                      whileHover={{
                        scale: 1.05,
                        boxShadow:
                          "0 25px 50px -12px rgba(255, 255, 255, 0.5), 0 15px 25px -5px rgba(255, 255, 255, 0.3)",
                        y: -3,
                      }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="flex items-center justify-center gap-3">
                        <motion.span
                          animate={{
                            scale: [1, 1.1, 1],
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }}
                          className="text-xl"
                        >
                          🚀
                        </motion.span>
                        Start Free Trial
                        <motion.span
                          animate={{
                            x: [0, 4, 0],
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }}
                          className="text-xl"
                        >
                          →
                        </motion.span>
                      </span>
                    </motion.a>
                  ) : (
                    <button
                      disabled
                      className="w-full bg-gray-300 text-gray-500 py-5 px-6 rounded-2xl font-bold cursor-not-allowed text-lg"
                    >
                      Start Free Trial
                    </button>
                  )}
                </div>

                {/* Features */}
                <div className="relative z-10">
                  <h4 className="text-blue-100 font-medium mb-6">
                    Everything you need:
                  </h4>
                  <div className="space-y-4">
                    {[
                      "Lawyer + 1 Free Assistant seat",
                      "Texas Personal Injury & Medical Malpractice",
                      "Unlimited research & drafts*",
                      "Medical Command Center, Insurance Battle Station, Damages Calculator",
                      "Client intake (Email + form)",
                      "Task & deadline management",
                      "Case management & Document upload",
                    ].map((feature, index) => (
                      <motion.div
                        key={index}
                        className="flex items-start gap-3 group/feature"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        whileHover={{ x: 4, transition: { duration: 0.2 } }}
                      >
                        <div className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center flex-shrink-0 mt-0.5 transition-all duration-200 group-hover/feature:bg-white/30 group-hover/feature:scale-110">
                          <svg
                            className="w-3 h-3 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <span className="text-white text-sm leading-relaxed">
                          {feature}
                        </span>
                      </motion.div>
                    ))}

                    {/* Add-ons */}
                    <div className="pt-4 border-t border-white/20 space-y-4">
                      <h5 className="text-blue-100 font-medium text-sm">
                        Optional Add-ons:
                      </h5>

                      <div className="flex items-start gap-3">
                        <div className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <svg
                            className="w-3 h-3 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <span className="text-white text-sm leading-relaxed">
                          Extra user - ${isAnnual ? "25" : "29"}/month
                        </span>
                      </div>
                    </div>

                    {/* Fair Use Disclaimer */}
                    <div className="mt-6 pt-4 border-t border-white/20">
                      <p className="text-white/70 text-xs text-center">
                        * Fair Use applies
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Contact Card - Custom Plan */}
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.3, ease: "easeOut" },
              }}
              className="group"
            >
              <div className="bg-white rounded-3xl shadow-md p-8 border border-gray-200 h-full transition-all duration-300 group-hover:shadow-xl group-hover:border-gray-300">
                {/* Header */}
                <div className="mb-8 text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Custom Plan
                  </h3>
                  <div className="mb-2">
                    <span className="text-3xl font-bold text-gray-700">
                      Let's Talk
                    </span>
                  </div>
                  <p className="text-gray-500">Tailored for larger firms</p>
                </div>

                {/* Contact Options */}
                <div className="space-y-6 mb-8">
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center flex-shrink-0">
                      <svg
                        className="w-5 h-5 text-gray-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg mb-1 text-gray-900">
                        Schedule a call
                      </h4>
                      <p className="text-gray-500 text-sm">
                        Get personalized advice from our team
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center flex-shrink-0">
                      <svg
                        className="w-5 h-5 text-gray-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg mb-1 text-gray-900">
                        Live demo
                      </h4>
                      <p className="text-gray-500 text-sm">
                        See AiLex in action with real cases
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center flex-shrink-0">
                      <svg
                        className="w-5 h-5 text-gray-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg mb-1 text-gray-900">
                        Custom quote
                      </h4>
                      <p className="text-gray-500 text-sm">
                        For larger firms with specific needs
                      </p>
                    </div>
                  </div>
                </div>

                {/* Contact Form */}
                <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200">
                  <form className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <input
                          type="text"
                          placeholder="First name"
                          className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <input
                          type="text"
                          placeholder="Last name"
                          className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div>
                      <input
                        type="email"
                        placeholder="Email address"
                        className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div>
                      <textarea
                        rows={3}
                        placeholder="How can we help?"
                        className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      />
                    </div>

                    <motion.button
                      type="submit"
                      className="w-full bg-gray-900 hover:bg-gray-800 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
                      whileHover={{
                        scale: 1.02,
                        boxShadow:
                          "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                      }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Get Custom Quote
                    </motion.button>
                  </form>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          {/* FAQ Section - Framer Style */}
          <motion.div
            className="max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="text-center mb-16">
              {/* Eyebrow for FAQ */}
              <motion.div
                className="flex justify-center mb-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="flex items-center gap-2 text-sm font-medium text-gray-600 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50 shadow-sm"
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                  viewport={{ once: true }}
                >
                  <motion.div
                    className="w-2 h-2 bg-gradient-to-r from-[#8b5cf6] to-[#a855f7] rounded-full"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.8, 1, 0.8],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                  FAQs
                </motion.div>
              </motion.div>
              <motion.h2
                className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                Frequently Asked{" "}
                <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                  Questions
                </span>
              </motion.h2>
            </div>

            <div className="space-y-2">
              {[
                {
                  question: "How does AiLex keep my data secure?",
                  answer:
                    "AiLex uses enterprise-grade security with end-to-end encryption, SOC 2 compliance, and secure cloud infrastructure. Your client data is protected with the highest industry standards and never used to train AI models.",
                },
                {
                  question: "Do you train your AI on client data?",
                  answer:
                    "No, we never train our AI models on your client data. All client information remains completely private and confidential. Our AI models are trained on publicly available legal resources and general legal knowledge.",
                },
                {
                  question: "Do I need to change my current systems?",
                  answer:
                    "AiLex is designed to integrate seamlessly with your existing workflow. You can start using it immediately without changing your current practice management systems. We also offer integrations with popular legal software.",
                },
                {
                  question: "How quickly can I get started?",
                  answer:
                    "You can start using AiLex immediately after signing up. Our platform requires no setup or installation - just create your account and begin working on your first case. Most attorneys are productive within their first hour.",
                },
                {
                  question: "What is the pricing structure for AiLex?",
                  answer:
                    "AiLex offers a simple Solo plan at $99/month that includes 2 users (lawyer + 1 assistant). You can add additional users for $29/month each, and an AI Receptionist for $29/month. No hidden fees or complex pricing tiers.",
                },
              ].map((faq, index) => {
                const isExpanded = expandedItems.includes(index + 100); // Offset to avoid conflicts

                return (
                  <motion.div
                    key={index}
                    className="border-b border-gray-200 last:border-b-0"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <button
                      className="w-full py-6 text-left flex items-center justify-between group hover:bg-gray-50/50 transition-colors duration-200"
                      onClick={() => {
                        setExpandedItems((prev) =>
                          isExpanded
                            ? prev.filter((i) => i !== index + 100)
                            : [...prev, index + 100]
                        );
                      }}
                    >
                      <span className="font-medium text-gray-900 text-lg pr-4 group-hover:text-blue-600 transition-colors duration-200">
                        {faq.question}
                      </span>
                      <motion.div
                        animate={{ rotate: isExpanded ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                        className="flex-shrink-0"
                      >
                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-200">
                          <svg
                            className="w-4 h-4 text-blue-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </div>
                      </motion.div>
                    </button>

                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="pb-6 pr-12">
                            <p className="text-gray-600 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Final Call to Action Section - In Horizontal Box */}
      <section id="contact" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-6">
          {/* Eyebrow for CTA - Outside the box */}
          <motion.div
            className="flex justify-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="flex items-center gap-2 text-sm font-medium text-gray-600 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50 shadow-sm"
              initial={{ opacity: 0, x: -10 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <motion.div
                className="w-2 h-2 bg-[#b8ff5c] rounded-full"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.8, 1, 0.8],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
              Get Started
            </motion.div>
          </motion.div>

          <motion.div
            className="bg-gradient-to-br from-[#052f5f] via-[#0c1c2d] to-[#1e3a8a] rounded-3xl p-16 lg:p-20 text-center relative overflow-hidden"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            whileHover={{
              y: -8,
              scale: 1.01,
              transition: { duration: 0.3, ease: "easeOut" },
            }}
          >
            {/* Cloudy Background Effects */}
            <div className="absolute inset-0 opacity-30">
              <div
                className="absolute top-0 right-0 w-2/3 h-2/3 transform translate-x-1/4 -translate-y-1/4 scale-150"
                style={{
                  background:
                    "radial-gradient(circle, rgba(30, 174, 219, 0.2) 0%, transparent 50%)",
                }}
              ></div>
              <div
                className="absolute bottom-0 left-0 w-3/4 h-3/4 transform -translate-x-1/4 translate-y-1/4 scale-125"
                style={{
                  background:
                    "radial-gradient(circle, rgba(60, 130, 246, 0.15) 0%, transparent 60%)",
                }}
              ></div>
            </div>

            <div className="relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
              >
                <h2 className="text-4xl lg:text-5xl font-bold text-white mb-8">
                  Get your first demand letter drafted today
                </h2>

                {/* 3-Step Flow Illustration */}
                <motion.div
                  className="flex flex-col md:flex-row items-center justify-center gap-12 mb-12"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <motion.div
                    className="flex flex-col items-center"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    viewport={{ once: true }}
                  >
                    <div className="relative">
                      <div className="w-20 h-20 bg-gradient-to-br from-white to-gray-100 rounded-3xl flex items-center justify-center mb-4 shadow-2xl shadow-black/20 border border-white/20">
                        <svg
                          className="w-10 h-10 text-gray-700"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                        </svg>
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">1</span>
                      </div>
                    </div>
                    <span className="text-white font-semibold text-lg">
                      Upload Document
                    </span>
                    <span className="text-white/70 text-sm mt-1">
                      Drop your case files
                    </span>
                  </motion.div>

                  <div className="hidden md:flex items-center">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-white/30 rounded-full"></div>
                      <div className="w-2 h-2 bg-white/50 rounded-full"></div>
                      <div className="w-2 h-2 bg-white/70 rounded-full"></div>
                    </div>
                  </div>
                  <div className="md:hidden flex justify-center">
                    <div className="flex flex-col space-y-1">
                      <div className="w-2 h-2 bg-white/30 rounded-full"></div>
                      <div className="w-2 h-2 bg-white/50 rounded-full"></div>
                      <div className="w-2 h-2 bg-white/70 rounded-full"></div>
                    </div>
                  </div>

                  <motion.div
                    className="flex flex-col items-center"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: 0.5 }}
                    viewport={{ once: true }}
                  >
                    <div className="relative">
                      <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mb-4 shadow-2xl shadow-blue-500/30 border border-white/10">
                        <img
                          src={ailexIcon}
                          alt="AiLex Logo"
                          className="w-10 h-10 object-contain filter brightness-0 invert"
                        />
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">2</span>
                      </div>
                    </div>
                    <span className="text-white font-semibold text-lg">
                      AiLex Processes
                    </span>
                    <span className="text-white/70 text-sm mt-1">
                      AI analyzes & drafts
                    </span>
                  </motion.div>

                  <div className="hidden md:flex items-center">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-white/30 rounded-full"></div>
                      <div className="w-2 h-2 bg-white/50 rounded-full"></div>
                      <div className="w-2 h-2 bg-white/70 rounded-full"></div>
                    </div>
                  </div>
                  <div className="md:hidden flex justify-center">
                    <div className="flex flex-col space-y-1">
                      <div className="w-2 h-2 bg-white/30 rounded-full"></div>
                      <div className="w-2 h-2 bg-white/50 rounded-full"></div>
                      <div className="w-2 h-2 bg-white/70 rounded-full"></div>
                    </div>
                  </div>

                  <motion.div
                    className="flex flex-col items-center"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: 0.7 }}
                    viewport={{ once: true }}
                  >
                    <div className="relative">
                      <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl flex items-center justify-center mb-4 shadow-2xl shadow-emerald-500/30 border border-white/10">
                        <svg
                          className="w-10 h-10 text-white"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M22,6C22,4.89 21.1,4 20,4H4C2.89,4 2,4.89 2,6V18C2,19.1 2.9,20 4,20H20C21.1,20 22,19.1 22,18V6M20,6L12,11L4,6H20M20,18H4V8L12,13L20,8V18Z" />
                        </svg>
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">3</span>
                      </div>
                    </div>
                    <span className="text-white font-semibold text-lg">
                      Demand Letter Ready
                    </span>
                    <span className="text-white/70 text-sm mt-1">
                      Professional & complete
                    </span>
                  </motion.div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  viewport={{ once: true }}
                >
                  {stripeCheckoutUrl ? (
                    <a
                      href={stripeCheckoutUrl}
                      className="inline-flex items-center gap-3 bg-white text-[#0c1c2d] px-10 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
                    >
                      Start Free Trial
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17 8l4 4m0 0l-4 4m4-4H3"
                        />
                      </svg>
                    </a>
                  ) : (
                    <button
                      disabled
                      className="inline-flex items-center gap-3 bg-gray-300 text-gray-500 px-10 py-4 rounded-xl font-bold text-lg cursor-not-allowed"
                    >
                      Preview config missing
                    </button>
                  )}
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
      <CookieNotice />
    </div>
    // </PreviewPasswordGate>
  );
}
